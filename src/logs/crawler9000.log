[2025-08-02 18:48:17,606] INFO [root:47] Logging setup complete.
[2025-08-02 18:48:17,606] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:48:17,606] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:48:44,245] INFO [root:47] Logging setup complete.
[2025-08-02 18:48:44,245] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:48:44,245] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:49:02,848] INFO [root:47] Logging setup complete.
[2025-08-02 18:49:02,848] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:49:02,848] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:52:35,235] INFO [root:47] Logging setup complete.
[2025-08-02 18:52:35,236] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:52:35,236] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:52:35,311] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:54:01,033] INFO [root:47] Logging setup complete.
[2025-08-02 18:54:01,033] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:54:01,033] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:54:01,112] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:55:17,338] INFO [root:47] Logging setup complete.
[2025-08-02 18:55:17,338] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:55:17,338] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:55:17,411] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:56:14,261] INFO [root:47] Logging setup complete.
[2025-08-02 18:56:14,261] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:56:14,261] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:56:14,339] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:56:14,404] INFO [crawler9000.main:101] Crawler9000 GUI started
[2025-08-02 19:02:39,344] INFO [root:47] Logging setup complete.
[2025-08-02 19:02:39,345] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 19:02:39,345] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 19:02:39,424] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:02:39,485] INFO [crawler9000.main:101] Crawler9000 GUI started
[2025-08-02 19:03:05,035] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:05,036] ERROR [crawler9000.main:24] Failed to emit error signal: 'MainWindow' object has no attribute 'log_text'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:22,460] INFO [crawler9000.main:40] User chose to retry after unhandled exception
[2025-08-02 19:03:24,721] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:24,721] ERROR [crawler9000.main:24] Failed to emit error signal: 'MainWindow' object has no attribute 'log_text'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:28,886] INFO [crawler9000.main:40] User chose to retry after unhandled exception
[2025-08-02 19:03:31,730] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:31,730] ERROR [crawler9000.main:24] Failed to emit error signal: 'MainWindow' object has no attribute 'log_text'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:37,583] INFO [crawler9000.main:40] User chose to retry after unhandled exception
[2025-08-02 19:03:43,084] INFO [crawler9000.gui.main_window:1209] Session auto-saved to /tmp/crawler9000_autosave/autosave_20250802_190343.json
[2025-08-02 19:03:43,086] INFO [crawler9000.utils.config:223] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 19:03:43,086] INFO [crawler9000.gui.main_window:1258] MainWindow closed gracefully with auto-save
[2025-08-02 19:03:43,086] INFO [crawler9000.main:108] Application exited with code: 0
