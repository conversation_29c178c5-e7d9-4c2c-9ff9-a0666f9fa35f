[2025-08-02 18:48:17,606] INFO [root:47] Logging setup complete.
[2025-08-02 18:48:17,606] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:48:17,606] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:48:44,245] INFO [root:47] Logging setup complete.
[2025-08-02 18:48:44,245] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:48:44,245] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:49:02,848] INFO [root:47] Logging setup complete.
[2025-08-02 18:49:02,848] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:49:02,848] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:52:35,235] INFO [root:47] Logging setup complete.
[2025-08-02 18:52:35,236] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:52:35,236] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:52:35,311] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:54:01,033] INFO [root:47] Logging setup complete.
[2025-08-02 18:54:01,033] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:54:01,033] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:54:01,112] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:55:17,338] INFO [root:47] Logging setup complete.
[2025-08-02 18:55:17,338] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:55:17,338] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:55:17,411] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:56:14,261] INFO [root:47] Logging setup complete.
[2025-08-02 18:56:14,261] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 18:56:14,261] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 18:56:14,339] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 18:56:14,404] INFO [crawler9000.main:101] Crawler9000 GUI started
[2025-08-02 19:02:39,344] INFO [root:47] Logging setup complete.
[2025-08-02 19:02:39,345] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 19:02:39,345] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 19:02:39,424] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:02:39,485] INFO [crawler9000.main:101] Crawler9000 GUI started
[2025-08-02 19:03:05,035] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:05,036] ERROR [crawler9000.main:24] Failed to emit error signal: 'MainWindow' object has no attribute 'log_text'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:22,460] INFO [crawler9000.main:40] User chose to retry after unhandled exception
[2025-08-02 19:03:24,721] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:24,721] ERROR [crawler9000.main:24] Failed to emit error signal: 'MainWindow' object has no attribute 'log_text'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:28,886] INFO [crawler9000.main:40] User chose to retry after unhandled exception
[2025-08-02 19:03:31,730] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:31,730] ERROR [crawler9000.main:24] Failed to emit error signal: 'MainWindow' object has no attribute 'log_text'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:03:37,583] INFO [crawler9000.main:40] User chose to retry after unhandled exception
[2025-08-02 19:03:43,084] INFO [crawler9000.gui.main_window:1209] Session auto-saved to /tmp/crawler9000_autosave/autosave_20250802_190343.json
[2025-08-02 19:03:43,086] INFO [crawler9000.utils.config:223] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 19:03:43,086] INFO [crawler9000.gui.main_window:1258] MainWindow closed gracefully with auto-save
[2025-08-02 19:03:43,086] INFO [crawler9000.main:108] Application exited with code: 0
[2025-08-02 21:05:13,643] INFO [root:47] Logging setup complete.
[2025-08-02 21:05:13,643] INFO [crawler9000.main:82] Starting Crawler9000...
[2025-08-02 21:05:13,643] INFO [crawler9000.main:86] Configuration loaded
[2025-08-02 21:05:13,831] INFO [crawler9000.core.url_manager:57] URLManager initialized with thread-safe PriorityQueue
[2025-08-02 21:05:13,831] INFO [crawler9000.core.content_parser:24] ContentParser initialized
[2025-08-02 21:05:13,831] WARNING [crawler9000.core.ai_integration:124] No AI API key provided. AI features will be disabled.
[2025-08-02 21:05:13,832] INFO [crawler9000.core.ai_integration:126] AIAnalyzer initialized with provider: deepseek
[2025-08-02 21:05:13,832] INFO [crawler9000.core.crawler_engine:53] CrawlerEngine initialized with all components
[2025-08-02 21:05:13,832] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 21:05:13,901] ERROR [crawler9000.gui.main_window:643] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 21:05:13,922] INFO [crawler9000.main:101] Crawler9000 GUI started
[2025-08-02 21:10:28,375] INFO [crawler9000.core.crawler_engine:63] Starting crawl with 1 URLs: ['https://api-docs.deepseek.com/']
[2025-08-02 21:10:28,376] INFO [crawler9000.core.async_url_fetcher:76] AsyncURLFetcher initialized
[2025-08-02 21:10:28,376] INFO [crawler9000.core.async_url_fetcher:115] HTTP session initialized with connection pooling
[2025-08-02 21:10:28,376] INFO [crawler9000.core.crawler_engine:161] Processing URL: https://api-docs.deepseek.com/ with metadata: {'priority': 1.0, 'depth': 0, 'parent_url': None, 'added_at': 1754179828.3756573, 'actual_priority': 1.0, 'queued_at': 1754179828.3756573}
[2025-08-02 21:10:29,397] INFO [crawler9000.core.async_url_fetcher:158] Successfully fetched https://api-docs.deepseek.com/ (42410 bytes)
[2025-08-02 21:10:29,425] INFO [crawler9000.core.content_parser:63] Parsed page: https://api-docs.deepseek.com/ (266 words, 4 links)
[2025-08-02 21:10:29,426] INFO [crawler9000.core.crawler_engine:250] Processed https://api-docs.deepseek.com/: 266 words, 3 new links
[2025-08-02 21:10:30,428] INFO [crawler9000.core.async_url_fetcher:122] HTTP session closed
[2025-08-02 21:10:30,464] INFO [crawler9000.core.crawler_engine:141] Crawl completed. Stats: {'visited_urls': 1, 'queued_urls': 0, 'total_discovered': 1, 'pages_crawled': 1, 'is_running': True, 'start_domain': 'api-docs.deepseek.com'}
