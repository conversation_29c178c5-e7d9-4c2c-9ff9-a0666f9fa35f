#!/usr/bin/env python3
"""
Crawler9000 - Main entry point for the application.

This module provides the main entry point for running Crawler9000 as a module:
    python -m crawler9000

It imports and calls the main function from the main.py module.
"""

import sys
import os

# Add the src directory to the Python path if needed
if __name__ == "__main__":
    # Get the directory containing this file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Get the src directory (parent of crawler9000)
    src_dir = os.path.dirname(current_dir)
    # Add to path if not already there
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)

    # Import and run the main function
    try:
        from crawler9000.main import main
        main()
    except ImportError as e:
        print(f"Error importing main module: {e}")
        print("Please ensure all dependencies are installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting Crawler9000: {e}")
        sys.exit(1)

def main():
    """Entry point for the application."""
    from crawler9000.main import main as app_main
    app_main()
