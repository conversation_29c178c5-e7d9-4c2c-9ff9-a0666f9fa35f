"""
Session Exporter for Crawler9000

This module provides comprehensive export functionality for crawl sessions,
supporting JSON and CSV formats with full session data serialization.
"""

import json
import csv
import io
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

logger = logging.getLogger(__name__)

from ..shared.models import (
    CrawlStatistics, URLData, PageContent, ActivityLogEntry, 
    ErrorInfo, AIAnalysis, CrawlStatus
)


class SessionExporter:
    """
    Comprehensive session export functionality for Crawler9000.
    
    Supports exporting complete session data including statistics, URLs,
    page content, activity logs, and AI analysis results to JSON and CSV formats.
    """
    
    def __init__(self, session_data: Dict[str, Any]):
        """
        Initialize the exporter with session data.
        
        Args:
            session_data: Dictionary containing all session information
        """
        self.session_data = session_data
        self.export_timestamp = datetime.now()
    
    def export_to_json(self, output_path: str, pretty_print: bool = True) -> bool:
        """
        Export entire session to JSON format.
        
        Args:
            output_path: Path where the JSON file will be saved
            pretty_print: Whether to format JSON with indentation
            
        Returns:
            bool: True if export successful, False otherwise
        """
        try:
            # Prepare export data with metadata
            export_data = {
                "export_metadata": {
                    "export_timestamp": self.export_timestamp.isoformat(),
                    "exporter_version": "1.0",
                    "data_format": "json"
                },
                "session_data": self._serialize_session_data()
            }
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                if pretty_print:
                    json.dump(export_data, f, indent=2, default=self._json_serializer)
                else:
                    json.dump(export_data, f, default=self._json_serializer)
            
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to JSON: {e}")
            return False
    
    def export_to_csv(self, output_dir: str) -> bool:
        """
        Export session data to multiple CSV files (one per data type).
        
        Args:
            output_dir: Directory where CSV files will be saved
            
        Returns:
            bool: True if export successful, False otherwise
        """
        try:
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            
            # Export each data type to separate CSV files
            success = True
            
            # Export statistics
            if 'statistics' in self.session_data:
                success &= self._export_statistics_csv(
                    os.path.join(output_dir, 'statistics.csv')
                )
            
            # Export URLs
            if 'urls' in self.session_data:
                success &= self._export_urls_csv(
                    os.path.join(output_dir, 'urls.csv')
                )
            
            # Export page content
            if 'page_content' in self.session_data:
                success &= self._export_page_content_csv(
                    os.path.join(output_dir, 'page_content.csv')
                )
            
            # Export activity logs
            if 'activity_logs' in self.session_data:
                success &= self._export_activity_logs_csv(
                    os.path.join(output_dir, 'activity_logs.csv')
                )
            
            # Export errors
            if 'errors' in self.session_data:
                success &= self._export_errors_csv(
                    os.path.join(output_dir, 'errors.csv')
                )
            
            # Export AI analysis
            if 'ai_analysis' in self.session_data:
                success &= self._export_ai_analysis_csv(
                    os.path.join(output_dir, 'ai_analysis.csv')
                )
            
            # Create export metadata file
            self._create_export_metadata_csv(
                os.path.join(output_dir, 'export_metadata.csv')
            )
            
            return success
            
        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False
    
    def _serialize_session_data(self) -> Dict[str, Any]:
        """
        Serialize session data for JSON export.
        
        Returns:
            Dict containing serialized session data
        """
        serialized = {}
        
        # Serialize statistics
        if 'statistics' in self.session_data:
            stats = self.session_data['statistics']
            if isinstance(stats, CrawlStatistics):
                serialized['statistics'] = self._dataclass_to_dict(stats)
            else:
                serialized['statistics'] = stats
        
        # Serialize URLs
        if 'urls' in self.session_data:
            urls = self.session_data['urls']
            serialized['urls'] = [
                self._dataclass_to_dict(url) if isinstance(url, URLData) else url
                for url in urls
            ]
        
        # Serialize page content
        if 'page_content' in self.session_data:
            pages = self.session_data['page_content']
            serialized['page_content'] = [
                self._dataclass_to_dict(page) if isinstance(page, PageContent) else page
                for page in pages
            ]
        
        # Serialize activity logs
        if 'activity_logs' in self.session_data:
            logs = self.session_data['activity_logs']
            serialized['activity_logs'] = [
                self._dataclass_to_dict(log) if isinstance(log, ActivityLogEntry) else log
                for log in logs
            ]
        
        # Serialize errors
        if 'errors' in self.session_data:
            errors = self.session_data['errors']
            serialized['errors'] = [
                self._dataclass_to_dict(error) if isinstance(error, ErrorInfo) else error
                for error in errors
            ]
        
        # Serialize AI analysis
        if 'ai_analysis' in self.session_data:
            analyses = self.session_data['ai_analysis']
            serialized['ai_analysis'] = [
                self._dataclass_to_dict(analysis) if isinstance(analysis, AIAnalysis) else analysis
                for analysis in analyses
            ]
        
        return serialized
    
    def _dataclass_to_dict(self, obj: Any) -> Dict[str, Any]:
        """
        Convert dataclass instance to dictionary.
        
        Args:
            obj: Dataclass instance
            
        Returns:
            Dictionary representation of the dataclass
        """
        if hasattr(obj, '__dataclass_fields__'):
            result = {}
            for field_name, field_def in obj.__dataclass_fields__.items():
                value = getattr(obj, field_name)
                if hasattr(value, '__dataclass_fields__'):
                    result[field_name] = self._dataclass_to_dict(value)
                elif isinstance(value, list):
                    result[field_name] = [
                        self._dataclass_to_dict(item) if hasattr(item, '__dataclass_fields__') else item
                        for item in value
                    ]
                else:
                    result[field_name] = value
            return result
        return obj
    
    def _json_serializer(self, obj: Any) -> str:
        """
        Custom JSON serializer for datetime and other objects.
        
        Args:
            obj: Object to serialize
            
        Returns:
            Serialized string representation
        """
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'value'):  # Enum objects
            return obj.value
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return str(obj)
    
    def _export_statistics_csv(self, output_path: str) -> bool:
        """Export statistics to CSV file."""
        try:
            stats = self.session_data['statistics']
            if isinstance(stats, CrawlStatistics):
                with open(output_path, 'w', newline='', encoding='utf-8') as f:
                    f.write(stats.to_csv())
            else:
                # Handle dictionary format
                with open(output_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=stats.keys())
                    writer.writeheader()
                    writer.writerow(stats)
            return True
        except Exception as e:
            logger.error(f"Error exporting statistics CSV: {e}")
            return False
    
    def _export_urls_csv(self, output_path: str) -> bool:
        """Export URLs to CSV file."""
        try:
            urls = self.session_data['urls']
            if not urls:
                return True
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                if isinstance(urls[0], URLData):
                    # Use the to_csv method from URLData
                    all_csv_data = []
                    for url in urls:
                        csv_data = url.to_csv()
                        all_csv_data.append(csv_data)
                    
                    # Combine all CSV data
                    f.write(all_csv_data[0])  # Header from first item
                    for csv_data in all_csv_data:
                        # Write only data rows (skip header)
                        lines = csv_data.strip().split('\n')
                        if len(lines) > 1:
                            f.write(lines[1] + '\n')
                else:
                    # Handle dictionary format
                    writer = csv.DictWriter(f, fieldnames=urls[0].keys())
                    writer.writeheader()
                    writer.writerows(urls)
            return True
        except Exception as e:
            print(f"Error exporting URLs CSV: {e}")
            return False
    
    def _export_page_content_csv(self, output_path: str) -> bool:
        """Export page content to CSV file."""
        try:
            pages = self.session_data['page_content']
            if not pages:
                return True
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                if isinstance(pages[0], PageContent):
                    # Use the to_csv method from PageContent
                    all_csv_data = []
                    for page in pages:
                        csv_data = page.to_csv()
                        all_csv_data.append(csv_data)
                    
                    # Combine all CSV data
                    f.write(all_csv_data[0])  # Header from first item
                    for csv_data in all_csv_data:
                        # Write only data rows (skip header)
                        lines = csv_data.strip().split('\n')
                        if len(lines) > 1:
                            f.write(lines[1] + '\n')
                else:
                    # Handle dictionary format
                    writer = csv.DictWriter(f, fieldnames=pages[0].keys())
                    writer.writeheader()
                    writer.writerows(pages)
            return True
        except Exception as e:
            print(f"Error exporting page content CSV: {e}")
            return False
    
    def _export_activity_logs_csv(self, output_path: str) -> bool:
        """Export activity logs to CSV file."""
        try:
            logs = self.session_data['activity_logs']
            if not logs:
                return True
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'activity_type', 'timestamp', 'level', 'message', 
                    'details', 'url', 'session_id'
                ])
                writer.writeheader()
                
                for log in logs:
                    if isinstance(log, ActivityLogEntry):
                        row = {
                            'activity_type': log.activity_type.value if hasattr(log.activity_type, 'value') else log.activity_type,
                            'timestamp': log.timestamp.isoformat() if isinstance(log.timestamp, datetime) else log.timestamp,
                            'level': log.level.value if hasattr(log.level, 'value') else log.level,
                            'message': log.message,
                            'details': json.dumps(log.details) if log.details else '',
                            'url': log.url or '',
                            'session_id': log.session_id or ''
                        }
                    else:
                        row = log
                    writer.writerow(row)
            return True
        except Exception as e:
            logger.error(f"Error exporting activity logs CSV: {e}")
            return False
    
    def _export_errors_csv(self, output_path: str) -> bool:
        """Export errors to CSV file."""
        try:
            errors = self.session_data['errors']
            if not errors:
                return True
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'error_id', 'timestamp', 'error_type', 'message', 
                    'url', 'stack_trace', 'recovery_action', 'is_recoverable', 'session_id'
                ])
                writer.writeheader()
                
                for error in errors:
                    if isinstance(error, ErrorInfo):
                        row = {
                            'error_id': error.error_id,
                            'timestamp': error.timestamp.isoformat() if isinstance(error.timestamp, datetime) else error.timestamp,
                            'error_type': error.error_type,
                            'message': error.message,
                            'url': error.url or '',
                            'stack_trace': error.stack_trace or '',
                            'recovery_action': error.recovery_action or '',
                            'is_recoverable': error.is_recoverable,
                            'session_id': error.session_id or ''
                        }
                    else:
                        row = error
                    writer.writerow(row)
            return True
        except Exception as e:
            print(f"Error exporting errors CSV: {e}")
            return False
    
    def _export_ai_analysis_csv(self, output_path: str) -> bool:
        """Export AI analysis to CSV file."""
        try:
            analyses = self.session_data['ai_analysis']
            if not analyses:
                return True
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'analysis_type', 'confidence', 'decision', 'reasoning', 
                    'metadata', 'processing_time', 'created_at'
                ])
                writer.writeheader()
                
                for analysis in analyses:
                    if isinstance(analysis, AIAnalysis):
                        row = {
                            'analysis_type': analysis.analysis_type.value if hasattr(analysis.analysis_type, 'value') else analysis.analysis_type,
                            'confidence': analysis.confidence,
                            'decision': analysis.decision,
                            'reasoning': analysis.reasoning,
                            'metadata': json.dumps(analysis.metadata) if analysis.metadata else '',
                            'processing_time': analysis.processing_time,
                            'created_at': analysis.created_at.isoformat() if isinstance(analysis.created_at, datetime) else analysis.created_at
                        }
                    else:
                        row = analysis
                    writer.writerow(row)
            return True
        except Exception as e:
            print(f"Error exporting AI analysis CSV: {e}")
            return False
    
    def _create_export_metadata_csv(self, output_path: str) -> bool:
        """Create export metadata CSV file."""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=[
                    'export_timestamp', 'exporter_version', 'data_format', 
                    'session_id', 'total_files_exported'
                ])
                writer.writeheader()
                
                session_id = ''
                if 'statistics' in self.session_data:
                    stats = self.session_data['statistics']
                    session_id = stats.session_id if isinstance(stats, CrawlStatistics) else stats.get('session_id', '')
                
                # Count exported files
                files_exported = []
                for data_type in ['statistics', 'urls', 'page_content', 'activity_logs', 'errors', 'ai_analysis']:
                    if data_type in self.session_data and self.session_data[data_type]:
                        files_exported.append(f"{data_type}.csv")
                
                writer.writerow({
                    'export_timestamp': self.export_timestamp.isoformat(),
                    'exporter_version': '1.0',
                    'data_format': 'csv',
                    'session_id': session_id,
                    'total_files_exported': len(files_exported)
                })
            return True
        except Exception as e:
            print(f"Error creating export metadata CSV: {e}")
            return False


def create_session_exporter(session_data: Dict[str, Any]) -> SessionExporter:
    """
    Factory function to create a SessionExporter instance.
    
    Args:
        session_data: Dictionary containing session data
        
    Returns:
        SessionExporter instance
    """
    return SessionExporter(session_data)
