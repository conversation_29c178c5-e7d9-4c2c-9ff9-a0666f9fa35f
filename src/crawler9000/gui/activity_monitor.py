from PyQt6.QtWidgets import (
    QTabWidget, QWidget, QVBoxLayout, QTextEdit, QPlainTextEdit, QListWidget, 
    QCheckBox, QHBoxLayout, QLabel, QLineEdit, QPushButton, QSlider, QDateTimeEdit,
    QGroupBox, QGridLayout, QSplitter, QFrame, QComboBox
)
from PyQt6.QtCore import Qt, QMutex, QTimer, QDateTime, pyqtSignal
from PyQt6.QtGui import (
    QSyntaxHighlighter, QTextCharFormat, QFont, QColor, QKeySequence, QShortcut,
    QTextCursor, QTextDocument
)
from PyQt6.QtCore import QRegularExpression
from ..utils.pyqt6_compat import create_safe_text_format
from ..utils.ring_buffer import ThreadSafeRingBuffer, LogEntry
from ..utils.config import get_config
import logging
import re
from datetime import datetime, timedelta
from typing import List, Optional

class AIDecisionLogHighlighter(QSyntaxHighlighter):
    """
    Custom syntax highlighter for AI Decision Log tab
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Define highlighting rules
        self.highlighting_rules = []
        
        # AI Decision keywords
        ai_format = create_safe_text_format(color="blue", weight=QFont.Weight.Bold)
        ai_patterns = [r'\bAI\b', r'\bDecision\b', r'\bAnalysis\b', r'\bCrawl\b']
        for pattern in ai_patterns:
            self.highlighting_rules.append((re.compile(pattern), ai_format.get_format()))
        
        # Log levels
        error_format = create_safe_text_format(color="red", weight=QFont.Weight.Bold)
        self.highlighting_rules.append((re.compile(r'\bERROR\b'), error_format.get_format()))
        
        warning_format = create_safe_text_format(color="orange", weight=QFont.Weight.Bold)
        self.highlighting_rules.append((re.compile(r'\bWARNING\b'), warning_format.get_format()))
        
        info_format = create_safe_text_format(color="green")
        self.highlighting_rules.append((re.compile(r'\bINFO\b'), info_format.get_format()))
    
    def highlightBlock(self, text):
        for pattern, format in self.highlighting_rules:
            for match in pattern.finditer(text):
                self.setFormat(match.start(), match.end() - match.start(), format)


class ActivityMonitor(QTabWidget):
    """
    Activity Monitor with multiple tabs for logging and content display.
    
    Tabs:
    - Live Browser
    - AI Decision Log
    - Content Discovery
    - Error Log
    """
    def __init__(self):
        super().__init__()
        # Configuration
        self.config = get_config().gui

        # Ring buffer initialization
        self.buffer = ThreadSafeRingBuffer(max_size=self.config.max_log_entries)

        # Initialize SignalBus first
        from ..shared.signal_bus import SignalType, register_component, connect_signal
        from ..shared.models import ActivityLogPayload, ErrorPayload, AIDecisionPayload

        self.COMPONENT_ID = "activity_monitor"

        # Register with SignalBus
        register_component(self.COMPONENT_ID, self)
        
        # Connect to relevant signals
        connect_signal(SignalType.ACTIVITY_LOG, self.handle_activity_log, self.COMPONENT_ID)
        connect_signal(SignalType.ERROR_OCCURRED, self.handle_error, self.COMPONENT_ID)
        connect_signal(SignalType.AI_DECISION, self.handle_ai_decision, self.COMPONENT_ID)

        self.mutex = QMutex()
        
        # Create tab widgets with proper containers
        self.create_tabs()

        # Initialize search and filter widgets
        self.init_search_and_filters()

        # Auto-scroll controls
        self.auto_scroll = True  # Auto-scroll enabled by default
        self.pause_scrolling = False  # Pause scrolling toggle

        # Keystroke bindings
        self.setup_shortcuts()

    def setup_shortcuts(self):
        """Setup keyboard shortcuts for search/navigation."""
        self.shortcut_find = QShortcut(QKeySequence('Ctrl+F'), self)
        self.shortcut_find.activated.connect(lambda: self.search_box.setFocus())
        self.shortcut_next = QShortcut(QKeySequence('F3'), self)
        self.shortcut_next.activated.connect(self.find_next)

    def init_search_and_filters(self):
        """Initialize components for searching and filtering logs."""
        filter_widget = QWidget()
        filter_layout = QGridLayout(filter_widget)

        # Search box with incremental search
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search logs (regex supported)...")
        self.search_box.textChanged.connect(self.incremental_search)
        self.search_box.setToolTip("Search logs using regex patterns")
        self.search_box.setWhatsThis("Search through log entries using regular expressions. This supports pattern matching for more advanced searches.")
        filter_layout.addWidget(QLabel("Search:"), 0, 0)
        filter_layout.addWidget(self.search_box, 0, 1)
        
        self.search_button = QPushButton("Find")
        self.search_button.clicked.connect(self.perform_search)
        self.search_button.setToolTip("Perform the search")
        self.search_button.setStatusTip("Find all matches for the search query")
        filter_layout.addWidget(self.search_button, 0, 2)
        
        self.clear_search_button = QPushButton("Clear")
        self.clear_search_button.clicked.connect(self.clear_search)
        self.clear_search_button.setToolTip("Clear search and highlights")
        self.clear_search_button.setStatusTip("Reset search field and remove highlighting")
        filter_layout.addWidget(self.clear_search_button, 0, 3)

        # Log level checkboxes
        log_level_group = QGroupBox("Log Levels")
        log_level_layout = QHBoxLayout(log_level_group)
        
        self.info_checkbox = QCheckBox("INFO")
        self.info_checkbox.setChecked(True)
        self.warning_checkbox = QCheckBox("WARNING")
        self.warning_checkbox.setChecked(True)
        self.error_checkbox = QCheckBox("ERROR")
        self.error_checkbox.setChecked(True)
        self.debug_checkbox = QCheckBox("DEBUG")
        self.debug_checkbox.setChecked(False)
        
        log_level_layout.addWidget(self.info_checkbox)
        log_level_layout.addWidget(self.warning_checkbox)
        log_level_layout.addWidget(self.error_checkbox)
        log_level_layout.addWidget(self.debug_checkbox)
        
        filter_layout.addWidget(log_level_group, 1, 0, 1, 4)

        # Time range sliders with date/time pickers
        time_group = QGroupBox("Time Range")
        time_layout = QGridLayout(time_group)
        
        # Start time
        time_layout.addWidget(QLabel("Start Time:"), 0, 0)
        self.start_time_picker = QDateTimeEdit(QDateTime.currentDateTime().addDays(-1))
        self.start_time_picker.setCalendarPopup(True)
        time_layout.addWidget(self.start_time_picker, 0, 1)
        
        # End time
        time_layout.addWidget(QLabel("End Time:"), 0, 2)
        self.end_time_picker = QDateTimeEdit(QDateTime.currentDateTime())
        self.end_time_picker.setCalendarPopup(True)
        time_layout.addWidget(self.end_time_picker, 0, 3)
        
        # Quick time range buttons
        time_buttons_layout = QHBoxLayout()
        self.last_hour_button = QPushButton("Last Hour")
        self.last_hour_button.clicked.connect(lambda: self.set_time_range(hours=1))
        self.last_day_button = QPushButton("Last Day")
        self.last_day_button.clicked.connect(lambda: self.set_time_range(days=1))
        self.last_week_button = QPushButton("Last Week")
        self.last_week_button.clicked.connect(lambda: self.set_time_range(days=7))
        
        time_buttons_layout.addWidget(self.last_hour_button)
        time_buttons_layout.addWidget(self.last_day_button)
        time_buttons_layout.addWidget(self.last_week_button)
        time_buttons_layout.addStretch()
        
        time_layout.addLayout(time_buttons_layout, 1, 0, 1, 4)
        filter_layout.addWidget(time_group, 2, 0, 1, 4)

        # Action buttons
        action_layout = QHBoxLayout()
        self.filter_button = QPushButton("Apply Filters")
        self.filter_button.clicked.connect(self.apply_filters)
        
        self.clear_filters_button = QPushButton("Clear Filters")
        self.clear_filters_button.clicked.connect(self.clear_filters)
        
        self.export_filtered_button = QPushButton("Export Filtered")
        self.export_filtered_button.clicked.connect(self.export_filtered_logs)
        
        action_layout.addWidget(self.filter_button)
        action_layout.addWidget(self.clear_filters_button)
        action_layout.addWidget(self.export_filtered_button)
        action_layout.addStretch()
        
        filter_layout.addLayout(action_layout, 3, 0, 1, 4)

        # Add filter widget to the main layout
        self.insertTab(0, filter_widget, "Search & Filters")

    def perform_search(self):
        """Perform regex search on logs."""
        query = self.search_box.text()
        if not query:
            return

        # Get all entries matching the search
        matches = self.buffer.search(query)
        
        # Populate the current tab with search results
        current_tab_index = self.currentIndex() - 1
        search_result_text = '\n'.join(str(entry) for entry in matches if entry is not None)
        self.get_tab_widget(current_tab_index).setPlainText(search_result_text)

    def apply_filters(self):
        """Filter logs by selected levels and time range."""
        levels = []
        if self.info_checkbox.isChecked():
            levels.append(logging.INFO)
        if self.warning_checkbox.isChecked():
            levels.append(logging.WARNING)
        if self.error_checkbox.isChecked():
            levels.append(logging.ERROR)
        if self.debug_checkbox.isChecked():
            levels.append(logging.DEBUG)

        start_time = self.start_time_picker.dateTime().toPyDateTime()
        end_time = self.end_time_picker.dateTime().toPyDateTime()

        # Apply combined filtering
        filtered_entries = self.buffer.combined_filter(
            levels=levels,
            start_time=start_time,
            end_time=end_time
        )

        # Populate the current tab with filtered results
        current_tab_index = self.currentIndex() - 1
        filter_result_text = '\n'.join(str(entry) for entry in filtered_entries if entry is not None)
        self.get_tab_widget(current_tab_index).setPlainText(filter_result_text)

    def incremental_search(self):
        """Perform incremental highlighting of search results."""
        search_text = self.search_box.text()
        if not search_text:
            return

        regex = QRegularExpression(search_text)
        for index in range(self.count()):
            current_edit = self.get_tab_widget(index)
            cursor = QTextCursor(current_edit.document())
            color_format = QTextCharFormat()
            color_format.setBackground(QColor("yellow"))

            while not cursor.isNull() and not cursor.atEnd():
                cursor = current_edit.document().find(regex, cursor)
                if not cursor.isNull():
                    cursor.mergeCharFormat(color_format)

    def set_time_range(self, days: int = 0, hours: int = 0):
        """Set the time range picker to a predefined span."""
        start_time = QDateTime.currentDateTime().addDays(-days).addSecs(-hours*3600)
        self.start_time_picker.setDateTime(start_time)
        self.end_time_picker.setDateTime(QDateTime.currentDateTime())

    def clear_filters(self):
        """Clear all filters to show all logs."""
        self.start_time_picker.setDateTime(QDateTime.currentDateTime().addDays(-1))
        self.end_time_picker.setDateTime(QDateTime.currentDateTime())
        self.info_checkbox.setChecked(True)
        self.warning_checkbox.setChecked(True)
        self.error_checkbox.setChecked(True)
        self.debug_checkbox.setChecked(False)
        tab_widgets = [self.live_browser, self.decision_log, self.error_log]
        for widget in tab_widgets:
            widget.clear()
            for entry in self.buffer.get_all():
                widget.append(str(entry))

    def export_filtered_logs(self):
        """Export the currently filtered logs to a file."""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox

        # Get the current tab's content
        current_tab = self.tab_widget.currentWidget()
        if not current_tab:
            QMessageBox.information(self, "Export", "No logs to export.")
            return

        # Get all text from the current tab
        content = current_tab.toPlainText()
        if not content.strip():
            QMessageBox.information(self, "Export", "No logs to export in current tab.")
            return

        # Get save file path
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Filtered Logs",
            "filtered_logs.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                QMessageBox.information(self, "Export", f"Logs exported to {file_path}")
                logger.info(f"Exported filtered log entries to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export logs: {e}")
                logger.error(f"Failed to export filtered logs: {e}")

    def clear_search(self):
        """Clear the search box and remove highlights."""
        self.search_box.clear()
        self.clear_highlight()

    def clear_highlight(self):
        """Clear all text highlights from the logs."""
        for index in range(self.count()):
            current_edit = self.get_tab_widget(index)
            cursor = QTextCursor(current_edit.document())
            color_format = QTextCharFormat()
            color_format.setBackground(QColor("transparent"))
            cursor.select(QTextCursor.Document)
            cursor.setCharFormat(color_format)

    def find_next(self):
        """Find the next occurrence of the search text."""
        search_text = self.search_box.text()
        if not search_text:
            return

        current_edit = self.get_tab_widget(self.currentIndex() - 1)
        cursor = current_edit.textCursor()
        document = current_edit.document()
        
        # Move cursor to next match
        regex = QRegularExpression(search_text)
        pos = cursor.position()
        cursor = document.find(regex, pos)
        if cursor.isNull():
            # Restart search from beginning
            cursor = document.find(regex, 0)

        if not cursor.isNull():
            current_edit.setTextCursor(cursor)

    def get_tab_widget(self, index: int) -> Optional[QPlainTextEdit]:
        """Get the text widget of a specific tab by index."""
        tab_widgets = {
            0: self.live_browser,
            1: self.decision_log,
            2: self.error_log
        }
        return tab_widgets.get(index)

    def create_tabs(self):
        """Create all tabs with proper layouts and controls"""
        
        # Tab 0: Live Browser
        live_browser_widget = QWidget()
        live_browser_layout = QVBoxLayout(live_browser_widget)
        
        # Controls for Live Browser
        live_browser_controls = QHBoxLayout()
        self.live_browser_auto_scroll = QCheckBox("Auto Scroll")
        self.live_browser_auto_scroll.setChecked(True)
        self.live_browser_auto_scroll.stateChanged.connect(self.toggle_auto_scroll)
        self.live_browser_auto_scroll.setToolTip("Automatically scroll to new log entries")
        self.live_browser_auto_scroll.setStatusTip("Enable or disable automatic scrolling to latest entries")
        live_browser_controls.addWidget(self.live_browser_auto_scroll)
        live_browser_controls.addStretch()
        
        live_browser_layout.addLayout(live_browser_controls)
        
        self.live_browser = QTextEdit()
        self.live_browser.setReadOnly(True)
        live_browser_layout.addWidget(self.live_browser)
        
        self.addTab(live_browser_widget, "Live Browser")
        
        # Tab 1: AI Decision Log with syntax highlighter
        decision_log_widget = QWidget()
        decision_log_layout = QVBoxLayout(decision_log_widget)
        
        # Controls for AI Decision Log
        decision_log_controls = QHBoxLayout()
        self.decision_log_auto_scroll = QCheckBox("Auto Scroll")
        self.decision_log_auto_scroll.setChecked(True)
        self.decision_log_auto_scroll.stateChanged.connect(self.toggle_auto_scroll)
        decision_log_controls.addWidget(self.decision_log_auto_scroll)
        decision_log_controls.addStretch()
        
        decision_log_layout.addLayout(decision_log_controls)
        
        self.decision_log = QPlainTextEdit()
        self.decision_log.setReadOnly(True)
        
        # Apply syntax highlighter to AI Decision Log
        self.highlighter = AIDecisionLogHighlighter(self.decision_log.document())
        
        decision_log_layout.addWidget(self.decision_log)
        
        self.addTab(decision_log_widget, "AI Decision Log")
        
        # Tab 2: Content Discovery
        content_discovery_widget = QWidget()
        content_discovery_layout = QVBoxLayout(content_discovery_widget)
        
        content_discovery_layout.addWidget(QLabel("Recently discovered content links:"))
        self.content_discovery = QListWidget()
        content_discovery_layout.addWidget(self.content_discovery)
        
        self.addTab(content_discovery_widget, "Content Discovery")
        
        # Tab 3: Error Log
        error_log_widget = QWidget()
        error_log_layout = QVBoxLayout(error_log_widget)
        
        # Controls for Error Log
        error_log_controls = QHBoxLayout()
        self.error_log_auto_scroll = QCheckBox("Auto Scroll")
        self.error_log_auto_scroll.setChecked(True)
        self.error_log_auto_scroll.stateChanged.connect(self.toggle_auto_scroll)
        error_log_controls.addWidget(self.error_log_auto_scroll)
        error_log_controls.addStretch()
        
        error_log_layout.addLayout(error_log_controls)
        
        self.error_log = QPlainTextEdit()
        self.error_log.setReadOnly(True)
        # Red-themed error log
        self.error_log.setStyleSheet("QPlainTextEdit { background-color: #fdd; color: #800; }")
        
        error_log_layout.addWidget(self.error_log)
        
        self.addTab(error_log_widget, "Error Log")

    # SignalBus event handlers
    def handle_activity_log(self, payload):
        """Handle activity log signals from SignalBus."""
        from ..shared.models import ActivityLogPayload, ActivityType
        
        if not isinstance(payload, ActivityLogPayload):
            return
            
        for entry in payload.entries:
            timestamp = entry.timestamp.strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {entry.message}"
            
            # Route to appropriate tab based on activity type
            if entry.activity_type == ActivityType.AI_DECISION:
                self.log_ai_decision(formatted_message)
            elif entry.activity_type == ActivityType.ERROR_OCCURRED:
                self.log_error(formatted_message)
            elif entry.activity_type in [ActivityType.PAGE_REQUEST, ActivityType.PAGE_PARSED]:
                self.log_live_browser(formatted_message)
            elif entry.activity_type == ActivityType.LINK_DISCOVERED:
                self.add_discovered_link(entry.url or "Unknown URL", entry.message)
            else:
                self.log_live_browser(formatted_message)
    
    def handle_error(self, payload):
        """Handle error signals from SignalBus."""
        from ..shared.models import ErrorPayload
        
        if not isinstance(payload, ErrorPayload):
            return
            
        error = payload.error
        timestamp = error.timestamp.strftime("%H:%M:%S")
        error_msg = f"[{timestamp}] ERROR: {error.message}"
        if error.url:
            error_msg += f" (URL: {error.url})"
            
        self.log_error(error_msg)
    
    def handle_ai_decision(self, payload):
        """Handle AI decision signals from SignalBus."""
        from ..shared.models import AIDecisionPayload
        
        if not isinstance(payload, AIDecisionPayload):
            return
            
        analysis = payload.analysis
        timestamp = analysis.created_at.strftime("%H:%M:%S")
        
        decision_msg = (
            f"[{timestamp}] AI Decision: {analysis.decision}\n"
            f"  Type: {analysis.analysis_type.value}\n"
            f"  Confidence: {analysis.confidence:.2f}\n"
            f"  Reasoning: {analysis.reasoning}"
        )
        
        self.log_ai_decision(decision_msg)

    def append_log(self, tab_index, text, level):
        """Append log text to a specific tab and store in ring buffer."""
        self.mutex.lock()
        try:
            # Store in ring buffer for searching/filtering
            log_entry = LogEntry(text, level)
            self.buffer.append(log_entry)
            
            # Map tab indices to the actual text widgets
            tab_widgets = {
                0: self.live_browser,
                1: self.decision_log,
                2: self.content_discovery,
                3: self.error_log
            }
            
            widget = tab_widgets.get(tab_index)
            if not widget:
                return

            if isinstance(widget, QTextEdit):
                widget.append(text)
                if self.auto_scroll and not self.pause_scrolling:
                    widget.verticalScrollBar().setValue(widget.verticalScrollBar().maximum())
            elif isinstance(widget, QPlainTextEdit):
                widget.appendPlainText(text)
                if self.auto_scroll and not self.pause_scrolling:
                    widget.verticalScrollBar().setValue(widget.verticalScrollBar().maximum())
            elif isinstance(widget, QListWidget):
                widget.addItem(text)
                if self.auto_scroll and not self.pause_scrolling:
                    widget.scrollToBottom()
        finally:
            self.mutex.unlock()

    def toggle_auto_scroll(self):
        """Toggle auto-scroll state."""
        self.auto_scroll = not self.auto_scroll
    
    def pause_scrolling_toggle(self):
        """Toggle pause scrolling state."""
        self.pause_scrolling = not self.pause_scrolling
    
    def add_discovered_link(self, url, description=""):
        """Add a discovered link to the Content Discovery tab."""
        if description:
            item_text = f"{url} - {description}"
        else:
            item_text = url
        self.append_log(2, item_text, logging.INFO)
    
    def log_ai_decision(self, decision_text):
        """Log an AI decision to the AI Decision Log tab."""
        self.append_log(1, decision_text, logging.INFO)
    
    def log_error(self, error_text):
        """Log an error to the Error Log tab."""
        self.append_log(3, error_text, logging.ERROR)
    
    def log_live_browser(self, browser_text):
        """Log browser activity to the Live Browser tab."""
        self.append_log(0, browser_text, logging.INFO)
    
    def clear_all_tabs(self):
        """Clear content from all tabs."""
        self.live_browser.clear()
        self.decision_log.clear()
        self.content_discovery.clear()
        self.error_log.clear()

    def get_all_logs(self) -> list:
        """Get all logs from all tabs for export."""
        logs = []

        # Get live browser logs
        live_browser_text = self.live_browser.toPlainText()
        if live_browser_text:
            logs.extend([
                {"type": "live_browser", "message": line.strip()}
                for line in live_browser_text.split('\n')
                if line.strip()
            ])

        # Get AI decision logs
        decision_text = self.decision_log.toPlainText()
        if decision_text:
            logs.extend([
                {"type": "ai_decision", "message": line.strip()}
                for line in decision_text.split('\n')
                if line.strip()
            ])

        # Get content discovery logs
        for i in range(self.content_discovery.count()):
            item = self.content_discovery.item(i)
            if item:
                logs.append({"type": "content_discovery", "message": item.text()})

        return logs

    def get_error_logs(self) -> list:
        """Get error logs for export."""
        error_text = self.error_log.toPlainText()
        if error_text:
            return [
                {"type": "error", "message": line.strip()}
                for line in error_text.split('\n')
                if line.strip()
            ]
        return []
