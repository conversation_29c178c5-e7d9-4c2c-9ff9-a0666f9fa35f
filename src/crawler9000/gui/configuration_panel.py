from datetime import datetime
from typing import Dict, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, QLineEdit, QComboBox,
    QSlider, QGroupBox, QCheckBox, QPushButton, QSpacerItem, QSizePolicy,
    QToolButton, QFrame, QSpinBox, QWhatsThis
)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QRect, QEasingCurve
from PyQt6.QtGui import QRegularExpressionValidator, QIntValidator, QIcon
from PyQt6.QtCore import QRegularExpression
from ..utils.config import AppConfig
from ..shared.signal_bus import SignalType, get_signal_bus, register_component, emit_signal
from ..shared.models import (
    ConfigChangePayload, CrawlerConfig, AIConfig, CrawlMode, StartCrawlCommand
)


class ConfigurationPanel(QWidget):
    """Configuration panel for crawler settings with fixed 400px width."""
    
    # Legacy Qt signal for backward compatibility
    configChanged = pyqtSignal(dict)
    
    COMPONENT_ID = "configuration_panel"

    def __init__(self):
        super().__init__()
        self.setFixedWidth(400)
        self.current_config = {}
        self.init_ui()
        self.setup_default_values()

        # Register with SignalBus
        register_component(self.COMPONENT_ID, self)

    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # URL Input
        url_group = QGroupBox("URL Configuration")
        url_layout = QVBoxLayout(url_group)
        
        url_label = QLabel("Starting URL:")
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://example.com")
        self.url_input.textChanged.connect(self.emit_config_changed)

        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input)
        layout.addWidget(url_group)

        # Crawl Mode
        mode_group = QGroupBox("Crawl Configuration")
        mode_layout = QVBoxLayout(mode_group)
        
        crawl_mode_label = QLabel("Crawl Mode:")
        self.crawl_mode_combo = QComboBox()
        self.crawl_mode_combo.addItems(["Documentation", "Research", "Targeted"])
        self.crawl_mode_combo.setCurrentIndex(1)  # Default to Research
        self.crawl_mode_combo.currentIndexChanged.connect(self.emit_config_changed)

        mode_layout.addWidget(crawl_mode_label)
        mode_layout.addWidget(self.crawl_mode_combo)
        layout.addWidget(mode_group)

        # Intelligence Level
        ai_group = QGroupBox("AI Configuration")
        ai_layout = QVBoxLayout(ai_group)
        
        intelligence_label = QLabel("Intelligence Level (1-5):")
        
        slider_layout = QHBoxLayout()
        self.intelligence_slider = QSlider(Qt.Orientation.Horizontal)
        self.intelligence_slider.setRange(1, 5)
        self.intelligence_slider.setValue(3)
        self.intelligence_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.intelligence_slider.setTickInterval(1)
        self.intelligence_slider.valueChanged.connect(self.emit_config_changed)

        self.intelligence_value_label = QLabel(str(self.intelligence_slider.value()))
        self.intelligence_value_label.setFixedWidth(20)
        self.intelligence_value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.intelligence_slider.valueChanged.connect(
            lambda: self.intelligence_value_label.setText(str(self.intelligence_slider.value()))
        )
        
        slider_layout.addWidget(self.intelligence_slider)
        slider_layout.addWidget(self.intelligence_value_label)

        ai_layout.addWidget(intelligence_label)
        ai_layout.addLayout(slider_layout)
        layout.addWidget(ai_group)

        # Advanced settings
        self.advanced_group = QGroupBox("Advanced Settings")
        self.advanced_group.setCheckable(True)
        self.advanced_group.setChecked(False)
        self.advanced_group.toggled.connect(self.emit_config_changed)

        advanced_layout = QVBoxLayout()

        # Depth setting
        depth_label = QLabel("Maximum Depth:")
        self.depth_input = QLineEdit()
        self.depth_input.setPlaceholderText("3")
        self.depth_input.textChanged.connect(self.emit_config_changed)
        
        # Max pages setting
        max_pages_label = QLabel("Maximum Pages:")
        self.max_pages_input = QLineEdit()
        self.max_pages_input.setPlaceholderText("100")
        self.max_pages_input.textChanged.connect(self.emit_config_changed)

        # Domain restrictions
        domain_restriction_label = QLabel("Allowed Domains (comma-separated):")
        self.allow_domains_input = QLineEdit()
        self.allow_domains_input.setPlaceholderText("example.com, subdomain.example.com")
        self.allow_domains_input.textChanged.connect(self.emit_config_changed)

        # Checkboxes
        self.follow_external_check = QCheckBox("Follow External Links")
        self.follow_external_check.setChecked(False)
        self.follow_external_check.stateChanged.connect(self.emit_config_changed)

        self.respect_robots_check = QCheckBox("Respect robots.txt")
        self.respect_robots_check.setChecked(True)
        self.respect_robots_check.stateChanged.connect(self.emit_config_changed)

        advanced_layout.addWidget(depth_label)
        advanced_layout.addWidget(self.depth_input)
        advanced_layout.addWidget(max_pages_label)
        advanced_layout.addWidget(self.max_pages_input)
        advanced_layout.addWidget(domain_restriction_label)
        advanced_layout.addWidget(self.allow_domains_input)
        advanced_layout.addWidget(self.follow_external_check)
        advanced_layout.addWidget(self.respect_robots_check)

        self.advanced_group.setLayout(advanced_layout)
        layout.addWidget(self.advanced_group)

        # Apply and Start buttons
        button_layout = QHBoxLayout()
        self.apply_button = QPushButton("Apply")
        self.apply_button.setEnabled(True)
        self.apply_button.clicked.connect(self.on_apply)
        button_layout.addWidget(self.apply_button)
        
        self.start_button = QPushButton("Start")
        self.start_button.setEnabled(True)
        self.start_button.clicked.connect(self.on_start)
        button_layout.addWidget(self.start_button)

        layout.addLayout(button_layout)
        
        # Add stretch to push everything to the top
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

    def setup_default_values(self):
        """Set up default values for all inputs."""
        self.url_input.setText("")
        self.crawl_mode_combo.setCurrentIndex(1)  # Research
        self.intelligence_slider.setValue(3)
        self.depth_input.setText("3")
        self.max_pages_input.setText("100")
        self.allow_domains_input.setText("")
        self.follow_external_check.setChecked(False)
        self.respect_robots_check.setChecked(True)
        self.emit_config_changed()
    
    def get_config(self) -> dict:
        """Get current configuration as dictionary."""
        return self.current_config.copy()
    
    def validate_inputs(self) -> tuple[bool, str]:
        """Validate all inputs and return (is_valid, error_message)."""
        if not self.url_input.text().strip():
            return False, "URL is required"
        
        # Validate numeric inputs
        try:
            depth = int(self.depth_input.text()) if self.depth_input.text() else 3
            if depth < 1 or depth > 50:
                return False, "Depth must be between 1 and 50"
        except ValueError:
            return False, "Depth must be a valid number"
        
        try:
            max_pages = int(self.max_pages_input.text()) if self.max_pages_input.text() else 100
            if max_pages < 1 or max_pages > 10000:
                return False, "Max pages must be between 1 and 10000"
        except ValueError:
            return False, "Max pages must be a valid number"
        
        return True, ""
    
    def emit_config_changed(self):
        """Emit configuration change signal with current values."""
        config = {
            "url": self.url_input.text().strip(),
            "crawl_mode": self.crawl_mode_combo.currentText(),
            "intelligence_level": self.intelligence_slider.value(),
            "advanced": {
                "enabled": self.advanced_group.isChecked(),
                "depth": self.depth_input.text() or "3",
                "max_pages": self.max_pages_input.text() or "100",
                "allow_domains": self.allow_domains_input.text().strip(),
                "follow_external": self.follow_external_check.isChecked(),
                "respect_robots": self.respect_robots_check.isChecked(),
            }
        }
        self.current_config = config
        
        # Emit legacy Qt signal for backward compatibility
        self.configChanged.emit(config)

    def on_apply(self):
        """Apply the current configuration without starting the crawl."""
        is_valid, error_message = self.validate_inputs()
        if not is_valid:
            print(f"Configuration error: {error_message}")
            return

        # Emit configuration change
        self.emit_config_changed()

    def on_start(self):
        """Apply the configuration and start crawling."""
        is_valid, error_message = self.validate_inputs()
        if not is_valid:
            print(f"Configuration error: {error_message}")
            return

        # Emit configuration change
        self.emit_config_changed()

    def show_error(self, message: str):
        """Display an error message to the user."""
        print(f"Error: {message}")
