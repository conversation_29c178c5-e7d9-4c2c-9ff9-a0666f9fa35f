"""
Main window for Crawler9000

This module contains the PyQt6 main window class
that initializes and controls the primary application interface.
"""
from PyQt6.QtWidgets import (
    QMainWindow, QDockWidget, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTabWidget, QLabel, QPushButton, QLineEdit, QTextEdit,
    QListWidget, QProgressBar, QGroupBox, QSpinBox, QCheckBox,
    QComboBox, QMenuBar, QStatusBar, QFrame, QScrollArea, QToolBar,
    QFileDialog, QMessageBox, QWhatsThis
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize
from PyQt6.QtGui import QFont, QIcon, QAction, QPixmap, QPainter, QPen, QBrush, QKeySequence, QShortcut
import logging
import psutil
import time
import json
from dataclasses import asdict
from datetime import datetime
from .configuration_panel import ConfigurationPanel
from .activity_monitor import ActivityMonitor
from ..utils.config import get_config, save_config, get_config_manager, CrawlerConfig, AIConfig, GUIConfig
from ..utils.session_exporter import SessionExporter
from ..shared.models import (
    CrawlStatistics, URLData, PageContent, CrawlStatus, CrawlMode,
    StartCrawlCommand, StopCrawlCommand,
    CrawlerConfig as ModelsCrawlerConfig, AIConfig as ModelsAIConfig
)
from ..shared.signal_bus import (
    SignalType, register_component, connect_signal, emit_signal, emit_command
)
from ..core.crawler_service import CrawlerService
from .statistics_dock import StatisticsDock
from .help_dialog import KeyboardReferenceDialog
from .theme_manager import ThemeManager

logger = logging.getLogger(__name__)

class UIRegistry:
    def __init__(self):
        self.docks = {}
        self.tabs = {}

    def register_dock(self, name, dock):
        self.docks[name] = dock

    def register_tab(self, name, tab):
        self.tabs[name] = tab


class MainWindow(QMainWindow):
    """
    Main application window with SignalBus integration.
    """
    
    COMPONENT_ID = "main_window"
    
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.setWindowTitle("Crawler9000 - AI-Powered Web Crawler")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize state variables
        self.is_crawling = False
        self.session_saved = True  # Track if session has unsaved changes
        self.last_auto_save_path = None  # Path for auto-save
        self.current_session_id = None  # Track current crawl session

        # Real crawler statistics tracking
        self.current_statistics = None  # Will hold real CrawlStatistics from crawler
        self.crawled_urls = []  # Real URLs crawled
        self.crawled_content = []  # Real page content

        # Initialize crawler service
        self.crawler_service = CrawlerService()
        self.setup_crawler_connections()
        
        # Register with SignalBus
        register_component(self.COMPONENT_ID, self)
        
        # Connect to configuration change signals
        connect_signal(
            SignalType.CONFIG_CHANGED, 
            self.handle_config_changed, 
            self.COMPONENT_ID
        )
        
        # Connect to status update signals
        connect_signal(
            SignalType.STATUS_UPDATE, 
            self.handle_status_update, 
            self.COMPONENT_ID
        )
        
        # Connect to page processed signals
        connect_signal(
            SignalType.PAGE_PROCESSED, 
            self.handle_page_processed, 
            self.COMPONENT_ID
        )
        
        # Initialize UI components
        self.init_ui()
        self.init_menu_bar()
        self.init_tool_bar()
        self.init_status_bar()
        
        # Load saved theme and layout
        self.load_saved_theme()
        self.load_layout_geometry()
        
        # Set up accessibility features
        self.setup_accessibility()
        
        # Initialize UI Registry
        self.ui_registry = UIRegistry()
        
        # Timer for throttled updates
        self.update_timer = QTimer()
        self.update_timer.setInterval(200)  # 200 ms throttling
        self.update_timer.timeout.connect(self.perform_throttled_update)

        # Setup keyboard shortcuts
        self.setup_shortcuts()
        self.update_timer.start()
        
        # Local state for coalescing updates
        self.pending_updates = {
            'status_updates': [],
            'page_processed': []
        }

    def setup_crawler_connections(self):
        """Set up connections to the crawler service."""
        # Connect crawler service signals to UI handlers
        self.crawler_service.status_update.connect(self.handle_crawler_status_update)
        self.crawler_service.page_processed.connect(self.handle_crawler_page_processed)
        self.crawler_service.activity_log.connect(self.handle_crawler_activity_log)
        self.crawler_service.error_occurred.connect(self.handle_crawler_error)
    
    def init_ui(self):
        # Central widget setup with QTabWidget
        self.central_tabs = QTabWidget()
        self.setCentralWidget(self.central_tabs)
        self.central_tabs.addTab(self.create_overview_tab(), "Overview")
        self.central_tabs.addTab(QWidget(), "URLs")        # Placeholder
        self.central_tabs.addTab(QWidget(), "Content")     # Placeholder
        self.central_tabs.addTab(QWidget(), "Stats")       # Placeholder
# Visualizations Tab with StatisticsDock
        self.statistics_dock = StatisticsDock()
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, self.statistics_dock)
        self.central_tabs.addTab(QWidget(), "Visualizations")

        # Dock widgets setup
        self.init_docks()
    
    def init_docks(self):
        """Initialize dock widgets for configuration panel and activity monitor."""
        # Configuration Panel Dock
        self.config_dock = QDockWidget("Configuration", self)
        self.configuration_panel = ConfigurationPanel()
        self.config_dock.setWidget(self.configuration_panel)
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, self.config_dock)
        
        # Connect configuration panel signals
        self.configuration_panel.configChanged.connect(self.on_config_changed)
        self.configuration_panel.configChanged.connect(self._mark_session_changed)
        
        # Activity Monitor Dock
        self.activity_dock = QDockWidget("Activity Monitor", self)
        self.activity_monitor = ActivityMonitor()
        self.activity_dock.setWidget(self.activity_monitor)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.activity_dock)
        
        # Make docks resizable
        self.config_dock.setFeatures(
            QDockWidget.DockWidgetFeature.DockWidgetMovable |
            QDockWidget.DockWidgetFeature.DockWidgetFloatable
        )
        self.activity_dock.setFeatures(
            QDockWidget.DockWidgetFeature.DockWidgetMovable |
            QDockWidget.DockWidgetFeature.DockWidgetFloatable
        )
    
    def create_control_panel(self):
        """Create the left control panel."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.Box)
        layout = QVBoxLayout(panel)
        
        # URL Input Section
        url_group = QGroupBox("URL Configuration")
        url_layout = QVBoxLayout(url_group)
        
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter starting URL...")
        self.url_input.setToolTip("Enter the URL where crawling should start.")
        QWhatsThis.enterWhatsThisMode()
        url_layout.addWidget(QLabel("Start URL:"))
        url_layout.addWidget(self.url_input)
        
        layout.addWidget(url_group)
        
        # Crawl Settings Section
        settings_group = QGroupBox("Crawl Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # Max pages
        settings_layout.addWidget(QLabel("Max Pages:"))
        self.max_pages_spin = QSpinBox()
        self.max_pages_spin.setRange(1, 10000)
        self.max_pages_spin.setToolTip("Set the maximum number of pages to crawl.")
        QWhatsThis.enterWhatsThisMode()
        self.max_pages_spin.setValue(100)
        settings_layout.addWidget(self.max_pages_spin)
        
        # Max depth
        settings_layout.addWidget(QLabel("Max Depth:"))
        self.max_depth_spin = QSpinBox()
        self.max_depth_spin.setRange(1, 20)
        self.max_depth_spin.setToolTip("Set the maximum depth for link following.")
        QWhatsThis.enterWhatsThisMode()
        self.max_depth_spin.setValue(3)
        settings_layout.addWidget(self.max_depth_spin)
        
        # Delay between requests
        settings_layout.addWidget(QLabel("Delay (seconds):"))
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 60)
        self.delay_spin.setToolTip("Set the delay in seconds between page requests.")
        QWhatsThis.enterWhatsThisMode()
        self.delay_spin.setValue(1)
        settings_layout.addWidget(self.delay_spin)
        
        # Options
        self.follow_external_cb = QCheckBox("Follow external links")
        settings_layout.addWidget(self.follow_external_cb)
        
        self.respect_robots_cb = QCheckBox("Respect robots.txt")
        self.respect_robots_cb.setChecked(True)
        settings_layout.addWidget(self.respect_robots_cb)
        
        layout.addWidget(settings_group)
        
        # AI Settings Section
        ai_group = QGroupBox("AI Configuration")
        ai_layout = QVBoxLayout(ai_group)
        
        ai_layout.addWidget(QLabel("Intelligence Level:"))
        self.intelligence_combo = QComboBox()
        self.intelligence_combo.addItems(["1 - Basic", "2 - Standard", "3 - Advanced", "4 - Expert", "5 - Maximum"])
        self.intelligence_combo.setToolTip("Select the AI intelligence level for processing.")
        QWhatsThis.enterWhatsThisMode()
        self.intelligence_combo.setCurrentIndex(2)
        ai_layout.addWidget(self.intelligence_combo)
        
        self.enable_content_analysis_cb = QCheckBox("Enable content analysis")
        self.enable_content_analysis_cb.setChecked(True)
        ai_layout.addWidget(self.enable_content_analysis_cb)
        
        self.enable_link_analysis_cb = QCheckBox("Enable link analysis")
        self.enable_link_analysis_cb.setChecked(True)
        ai_layout.addWidget(self.enable_link_analysis_cb)
        
        layout.addWidget(ai_group)
        
        # Control Buttons
        button_layout = QVBoxLayout()
        
        self.start_button = QPushButton("Start Crawling")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        self.start_button.clicked.connect(self.on_start_crawling)
        button_layout.addWidget(self.start_button)
        self.start_button.setToolTip("Start the crawling process.")
        QWhatsThis.enterWhatsThisMode()
        
        self.stop_button = QPushButton("Stop Crawling")
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }")
        self.stop_button.clicked.connect(self.on_stop_crawling)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        self.stop_button.setToolTip("Stop the crawling process.")
        QWhatsThis.enterWhatsThisMode()
        
        self.reset_button = QPushButton("Reset")
        self.reset_button.clicked.connect(self.on_reset)
        button_layout.addWidget(self.reset_button)
        self.reset_button.setToolTip("Reset the crawling session.")
        QWhatsThis.enterWhatsThisMode()
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return panel
    
    def create_overview_tab(self):
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)
        welcome_label = QLabel("Welcome to Crawler9000")
        welcome_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        overview_layout.addWidget(welcome_label)
        return overview_tab
    

    
    def init_menu_bar(self):
        """Initialize the menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        new_action = QAction('New Session', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.on_new_session)
        file_menu.addAction(new_action)
        
        open_action = QAction('Open Session', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.on_open_session)
        file_menu.addAction(open_action)
        
        save_action = QAction('Save Session', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.on_save_session)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        
        settings_action = QAction('Settings', self)
        settings_action.triggered.connect(self.on_settings)
        tools_menu.addAction(settings_action)
        
        # View menu
        view_menu = menubar.addMenu('View')
        
        # Theme submenu
        theme_menu = view_menu.addMenu('Theme')
        
        system_theme_action = QAction('System', self)
        system_theme_action.triggered.connect(lambda: self.change_theme('system'))
        theme_menu.addAction(system_theme_action)
        
        light_theme_action = QAction('Light', self)
        light_theme_action.triggered.connect(lambda: self.change_theme('light'))
        theme_menu.addAction(light_theme_action)
        
        dark_theme_action = QAction('Dark', self)
        dark_theme_action.triggered.connect(lambda: self.change_theme('dark'))
        theme_menu.addAction(dark_theme_action)
        
        custom_theme_action = QAction('Custom', self)
        custom_theme_action.triggered.connect(lambda: self.change_theme('custom'))
        theme_menu.addAction(custom_theme_action)
        
        view_menu.addSeparator()
        
        restore_defaults_action = QAction('Restore Default Layout', self)
        restore_defaults_action.triggered.connect(self.restore_default_layout)
        view_menu.addAction(restore_defaults_action)

        # Help menu
        help_menu = menubar.addMenu('Help')
        
        keyboard_ref_action = QAction('&Keyboard Reference', self)
        keyboard_ref_action.setShortcut('F1')
        keyboard_ref_action.triggered.connect(self.show_help)
        help_menu.addAction(keyboard_ref_action)
        
        help_menu.addSeparator()
        
        about_action = QAction('&About', self)
        about_action.triggered.connect(self.on_about)
        help_menu.addAction(about_action)
    
    def setup_shortcuts(self):
        """Setup global keyboard shortcuts."""
        QShortcut(QKeySequence('Ctrl+N'), self).activated.connect(self.on_new_session)
        QShortcut(QKeySequence('Ctrl+P'), self).activated.connect(self.on_pause)
        QShortcut(QKeySequence('F1'), self).activated.connect(self.show_help)
    
    def setup_accessibility(self):
        """Set up accessibility features including ARIA labels and tab order."""
        # Main window accessibility
        self.setAccessibleName("Crawler9000 Main Window")
        self.setAccessibleDescription("AI-powered web crawler main application window")
        
        # Central tabs accessibility
        self.central_tabs.setAccessibleName("Main Content Tabs")
        self.central_tabs.setAccessibleDescription("Tabbed interface showing crawler overview, URLs, content, and statistics")
        
        # Dock widgets accessibility
        if hasattr(self, 'config_dock'):
            self.config_dock.setAccessibleName("Configuration Panel")
            self.config_dock.setAccessibleDescription("Panel for configuring crawler settings and AI parameters")
        
        if hasattr(self, 'activity_dock'):
            self.activity_dock.setAccessibleName("Activity Monitor")
            self.activity_dock.setAccessibleDescription("Real-time activity monitor showing crawling progress and AI decisions")
        
        if hasattr(self, 'statistics_dock'):
            self.statistics_dock.setAccessibleName("Statistics Dashboard")
            self.statistics_dock.setAccessibleDescription("Visual dashboard showing crawling statistics and performance metrics")
        
        # Toolbar accessibility
        if hasattr(self, 'tool_progress_bar'):
            self.tool_progress_bar.setAccessibleName("Crawling Progress")
            self.tool_progress_bar.setAccessibleDescription("Progress indicator showing current crawling completion percentage")
        
        if hasattr(self, 'status_led_icon'):
            self.status_led_icon.setAccessibleName("Connection Status Indicator")
            self.status_led_icon.setAccessibleDescription("Visual indicator showing network connection status - green for connected, red for disconnected")
        
        # Status bar accessibility
        if hasattr(self, 'operation_status'):
            self.operation_status.setAccessibleName("Current Operation")
            self.operation_status.setAccessibleDescription("Current crawler operation status")
        
        if hasattr(self, 'pages_crawled_status'):
            self.pages_crawled_status.setAccessibleName("Pages Crawled Count")
            self.pages_crawled_status.setAccessibleDescription("Number of pages successfully crawled")
        
        if hasattr(self, 'elapsed_time_status'):
            self.elapsed_time_status.setAccessibleName("Session Elapsed Time")
            self.elapsed_time_status.setAccessibleDescription("Time elapsed since session started")
        
        if hasattr(self, 'memory_usage_status'):
            self.memory_usage_status.setAccessibleName("Memory Usage")
            self.memory_usage_status.setAccessibleDescription("Current application memory usage")
        
        if hasattr(self, 'connection_status'):
            self.connection_status.setAccessibleName("Network Connection Status")
            self.connection_status.setAccessibleDescription("Current network connection status")
    
    def on_pause(self):
        """Toggle pause/resume crawling."""
        if self.is_crawling:
            self.on_stop_crawling()
            self.log_message("Crawling paused")
        else:
            self.log_message("No active crawling to pause")

    def init_tool_bar(self):
        """Initialize the toolbar with actions."""
        tool_bar = QToolBar("Main Toolbar")
        self.addToolBar(tool_bar)

        # Start button
        start_action = QAction(QIcon(), 'Start', self)
        start_action.setStatusTip('Start crawling')
        start_action.triggered.connect(self.on_start_crawling)
        tool_bar.addAction(start_action)

        # Stop button
        stop_action = QAction(QIcon(), 'Stop', self)
        stop_action.setStatusTip('Stop crawling')
        stop_action.triggered.connect(self.on_stop_crawling)
        tool_bar.addAction(stop_action)

        # Spacer
        tool_bar.addSeparator()

        # Settings button
        settings_action = QAction(QIcon(), 'Settings', self)
        settings_action.setStatusTip('Open settings')
        settings_action.triggered.connect(self.on_settings)
        tool_bar.addAction(settings_action)

        # Status LED indicator
        self.status_led_icon = QLabel()
        self.update_status_led(True) # Connected at start
        tool_bar.addWidget(self.status_led_icon)

        # Progress bar
        self.tool_progress_bar = QProgressBar()
        self.tool_progress_bar.setMaximumWidth(200)
        self.tool_progress_bar.setValue(0)
        tool_bar.addWidget(self.tool_progress_bar)
    
    def init_status_bar(self):
        """Initialize the status bar with operation status, elapsed time, etc."""
        self.status_bar = self.statusBar() 
        self.status_bar.showMessage("Ready")

        # Operation status
        self.operation_status = QLabel("Operation: Idle")
        self.status_bar.addWidget(self.operation_status)

        # Pages crawled
        self.pages_crawled_status = QLabel("Pages crawled: 0")
        self.status_bar.addWidget(self.pages_crawled_status)

        # Elapsed time
        self.elapsed_time_status = QLabel("Elapsed: 00:00:00")
        self.status_bar.addWidget(self.elapsed_time_status)
        self.start_time = time.time()

        # Memory usage
        self.memory_usage_status = QLabel("Memory usage: 0 MB")
        self.status_bar.addWidget(self.memory_usage_status)

        # Connection status
        self.connection_status = QLabel("Connected: Yes") # Assume default is connected
        self.status_bar.addWidget(self.connection_status)

        # Update status periodically
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.update_elapsed_time)
        self.status_timer.start(1000)

        self.memory_timer = QTimer(self)
        self.memory_timer.timeout.connect(self.update_memory_usage)
        self.memory_timer.start(5000)  # Update memory usage every 5 seconds
    
    def update_memory_usage(self):
        """Update memory usage status."""
        memory_info = psutil.virtual_memory()
        used_memory_mb = memory_info.used / (1024 ** 2)
        self.memory_usage_status.setText(f"Memory usage: {used_memory_mb:.1f} MB")

    def update_elapsed_time(self):
        """Update elapsed time status."""
        elapsed_seconds = int(time.time() - self.start_time)
        hours, remainder = divmod(elapsed_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        self.elapsed_time_status.setText(f"Elapsed: {hours:02}:{minutes:02}:{seconds:02}")

    def update_status_bar(self, operation=None, pages_crawled=None, connected=None):
        """Update status bar elements."""
        if operation is not None:
            self.operation_status.setText(f"Operation: {operation}")
        if pages_crawled is not None:
            self.pages_crawled_status.setText(f"Pages crawled: {pages_crawled}")
        if connected is not None:
            status = "Yes" if connected else "No"
            self.connection_status.setText(f"Connected: {status}")
    
    def update_status_led(self, connected):
        """Update status LED indicator."""
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(pixmap)
        painter.setPen(QPen(Qt.GlobalColor.black, 1))
        painter.setBrush(QBrush(Qt.GlobalColor.green if connected else Qt.GlobalColor.red, Qt.BrushStyle.SolidPattern))
        painter.drawEllipse(0, 0, 20, 20)
        painter.end()
        self.status_led_icon.setPixmap(pixmap)

    def load_window_geometry(self):
        """Load window geometry from config."""
        gui_config = self.config.gui
        if gui_config.remember_window_state:
            self.resize(gui_config.window_width, gui_config.window_height)
            
    def restore_default_layout(self):
        """Restore the default layout of docks and toolbars."""
        # Reset dock positions
        self.config_dock.setFloating(False)
        self.activity_dock.setFloating(False)
        self.statistics_dock.setFloating(False)
        
        # Reset dock areas
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, self.config_dock)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.activity_dock)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, self.statistics_dock)
        
        # Reset window geometry
        self.resize(1400, 900)
        
        # Apply system theme
        self.theme_manager.apply_theme('system')
        
        # Save the reset configuration
        self.save_layout_geometry()
        
        self.log_message("Default layout restored")
        
    def load_saved_theme(self):
        """Load the saved theme from the configuration."""
        gui_config = get_config().gui
        self.theme_manager.apply_theme(gui_config.theme)
    
    def change_theme(self, theme_name: str):
        """Change the application theme and save the preference."""
        self.theme_manager.apply_theme(theme_name)
        
        # Save theme preference to configuration
        config = get_config()
        config.gui.theme = theme_name
        save_config(config)
        
        self.log_message(f"Theme changed to: {theme_name}")
    
    def save_layout_geometry(self):
        """Save window and dock geometry to config."""
        try:
            config = get_config()
            gui_config = config.gui
            
            # Save window geometry
            gui_config.window_width = self.width()
            gui_config.window_height = self.height()
            
            # Save dock geometry state
            if gui_config.save_dock_geometry:
                gui_config.dock_state = self.saveState().data()
            
            # Save current theme
            gui_config.theme = self.theme_manager.current_theme
            
            save_config(config)
            
        except Exception as e:
            logger.error(f"Error saving layout geometry: {e}")
    
    def load_layout_geometry(self):
        """Load window and dock geometry from config."""
        try:
            config = get_config()
            gui_config = config.gui
            
            if gui_config.remember_window_state:
                # Restore window geometry
                self.resize(gui_config.window_width, gui_config.window_height)
                
                # Restore dock geometry
                if gui_config.save_dock_geometry and gui_config.dock_state:
                    self.restoreState(gui_config.dock_state)
                    
        except Exception as e:
            logger.error(f"Error loading layout geometry: {e}")
    
    def save_window_geometry(self):
        """Save window geometry to config (deprecated - use save_layout_geometry)."""
        self.save_layout_geometry()

    def log_message(self, message):
        """Add a message to the activity log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # Log to ActivityMonitor's Live Browser tab if available
        if hasattr(self, 'activity_monitor'):
            self.activity_monitor.log_live_browser(formatted_message)
        else:
            # Fallback: use proper logging if ActivityMonitor is not available
            logger.info(formatted_message)
    
    def update_status(self, status, pages_crawled=None, total_pages=None, current_url=None):
        """Update the status display."""
        # Update status bar message
        self.status_bar.showMessage(status)

        # Update status bar with pages crawled and operation status
        if pages_crawled is not None:
            self.update_status_bar(operation=status, pages_crawled=pages_crawled)

        # Log status changes to ActivityMonitor
        if pages_crawled is not None and total_pages is not None:
            self.log_message(f"Status: {status} - Pages: {pages_crawled}/{total_pages}")
        elif current_url:
            self.log_message(f"Status: {status} - Current URL: {current_url}")
        else:
            self.log_message(f"Status: {status}")
    
    # SignalBus event handlers
    def handle_config_changed(self, payload):
        """Handle configuration change signals from SignalBus."""
        from ..shared.models import ConfigChangePayload
        
        if not isinstance(payload, ConfigChangePayload):
            return
            
        # Log the configuration change
        if payload.crawler_config:
            crawler_config = payload.crawler_config
            self.log_message(
                f"Configuration updated via SignalBus: {crawler_config.crawl_mode.value} mode, "
                f"Max pages: {crawler_config.max_pages}, Max depth: {crawler_config.max_depth}"
            )
            
        if payload.ai_config:
            ai_config = payload.ai_config
            self.log_message(f"AI config updated: Intelligence level {ai_config.intelligence_level}")
    
    # Event handlers
    def on_config_changed(self, config):
        """Handle configuration changes from the configuration panel (legacy)."""
        self.log_message(f"Configuration updated: {config['crawl_mode']} mode, Intelligence level {config['intelligence_level']}")
    
    def get_current_config(self):
        """Get current configuration from the configuration panel."""
        return self.configuration_panel.get_config()
    
    def validate_and_start_crawl(self):
        """Validate inputs and start crawling if valid."""
        is_valid, error_msg = self.configuration_panel.validate_inputs()
        if not is_valid:
            self.log_message(f"ERROR: {error_msg}")
            return False
        return True

    def on_start_crawling(self):
        """Handle start crawling button click."""
        # Prevent multiple starts
        if self.is_crawling:
            self.log_message("Crawling is already in progress")
            return

        # Validate inputs first
        if not self.validate_and_start_crawl():
            return

        # Get current configuration from the configuration panel
        config = self.get_current_config()

        url = config["url"]
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Update UI state
        self.is_crawling = True

        self.log_message(f"Starting crawl of {url}")
        max_pages = int(config["advanced"]["max_pages"]) if config["advanced"]["max_pages"] else 100
        self.update_status("Starting...", 0, max_pages)

        # Start actual crawling using the crawler service
        self.start_real_crawling(url, config)
    
    def start_real_crawling(self, url: str, config: dict):
        """Start real web crawling using the crawler service."""
        try:
            # Convert configuration panel data to proper config objects
            crawler_config = self.create_crawler_config(url, config)
            ai_config = self.create_ai_config(config)

            # Create start crawl command
            start_command = StartCrawlCommand(
                config=crawler_config,
                ai_config=ai_config
            )

            # Store session ID for tracking
            self.current_session_id = start_command.session_id

            # Start the crawler service
            self.crawler_service.start(start_command)

            self.log_message(f"Real crawling started for {url}")

        except Exception as e:
            self.log_message(f"ERROR: Failed to start crawling: {e}")
            self.is_crawling = False
            self.update_status("Error")

    def create_crawler_config(self, url: str, config: dict) -> ModelsCrawlerConfig:
        """Create CrawlerConfig from configuration panel data."""
        # Map crawl mode string to enum
        crawl_mode_map = {
            "Documentation": CrawlMode.DOCUMENTATION,
            "Research": CrawlMode.RESEARCH,
            "Targeted": CrawlMode.TARGETED
        }

        crawl_mode = crawl_mode_map.get(config.get("crawl_mode", "Research"), CrawlMode.RESEARCH)

        return ModelsCrawlerConfig(
            start_url=url,
            crawl_mode=crawl_mode,
            max_pages=int(config["advanced"]["max_pages"]) if config["advanced"]["max_pages"] else 100,
            max_depth=int(config["advanced"]["depth"]) if config["advanced"]["depth"] else 3,
            delay_between_requests=1.0,  # Default delay
            timeout=30,  # Default timeout
            follow_external_links=config["advanced"]["follow_external"],
            respect_robots_txt=config["advanced"]["respect_robots"],
            allowed_domains=config["advanced"]["allow_domains"].split(",") if config["advanced"]["allow_domains"] else [],
            max_file_size_mb=10  # Default file size limit
        )

    def create_ai_config(self, config: dict) -> ModelsAIConfig:
        """Create AIConfig from configuration panel data."""
        return ModelsAIConfig(
            intelligence_level=config.get("intelligence_level", 3),
            enable_content_analysis=True,
            enable_link_analysis=True,
            enable_smart_filtering=True,
            content_relevance_threshold=0.5,
            decision_confidence_threshold=0.7
        )

    def on_stop_crawling(self):
        """Handle stop crawling button click."""
        self.is_crawling = False

        # Stop the crawler service if running
        if self.current_session_id:
            stop_command = StopCrawlCommand(session_id=self.current_session_id)
            self.crawler_service.stop(stop_command)

        self.log_message("Crawling stopped by user")
        self.update_status("Stopped")
    
    def on_reset(self):
        """Handle reset button click."""
        self.is_crawling = False

        # Clear real crawler data
        self.current_statistics = None
        self.crawled_urls.clear()
        self.crawled_content.clear()
        self.current_session_id = None

        # Stop any running crawler
        if hasattr(self, 'crawler_service') and self.crawler_service:
            try:
                if self.current_session_id:
                    stop_command = StopCrawlCommand(session_id=self.current_session_id)
                    self.crawler_service.stop(stop_command)
            except Exception as e:
                self.log_message(f"Warning: Error stopping crawler during reset: {e}")

        # Clear ActivityMonitor tabs
        if hasattr(self, 'activity_monitor'):
            self.activity_monitor.clear_all_tabs()

        # Reset toolbar progress bar
        if hasattr(self, 'tool_progress_bar'):
            self.tool_progress_bar.setValue(0)

        self.restore_default_layout()  # Restore default layout on reset

        self.update_status("Ready", 0, 0, "None")
        self.log_message("Session reset")

    # Crawler service event handlers
    def handle_crawler_status_update(self, payload):
        """Handle status updates from the crawler service."""
        try:
            status = payload.status.value if hasattr(payload.status, 'value') else str(payload.status)
            pages_crawled = payload.statistics.pages_crawled if hasattr(payload, 'statistics') else 0
            urls_discovered = payload.statistics.urls_discovered if hasattr(payload, 'statistics') else 0

            # Store real statistics for export and display
            if hasattr(payload, 'statistics'):
                self.current_statistics = payload.statistics

            self.update_status(f"Crawling - {status}", pages_crawled, urls_discovered)
            self.log_message(f"Crawler status: {status} - Pages: {pages_crawled}, URLs: {urls_discovered}")

        except Exception as e:
            self.log_message(f"Error handling status update: {e}")

    def handle_crawler_page_processed(self, payload):
        """Handle page processed events from the crawler service."""
        try:
            # Extract data from PageProcessedPayload structure
            page_content = payload.page_content if hasattr(payload, 'page_content') else None
            if not page_content:
                self.log_message("Warning: Received page processed signal without page content")
                return

            url = page_content.url
            title = page_content.title

            # Store real crawled data for export
            url_data = URLData(
                url=url,
                title=title,
                depth=1,  # TODO: Get actual depth from payload
                is_processed=True,
                status_code=200  # TODO: Get actual status code from payload
            )
            self.crawled_urls.append(url_data)

            # Store page content
            self.crawled_content.append(page_content)

            self.log_message(f"Crawled: {url} ({page_content.word_count} words)")

            # Log to ActivityMonitor
            if hasattr(self, 'activity_monitor'):
                self.activity_monitor.log_live_browser(f"Processed: {url}")
                self.activity_monitor.add_discovered_link(url, title)

        except Exception as e:
            self.log_message(f"Error handling page processed: {e}")

    def handle_crawler_activity_log(self, payload):
        """Handle activity log events from the crawler service."""
        try:
            from ..shared.models import ActivityLogPayload

            # Handle ActivityLogPayload objects properly
            if isinstance(payload, ActivityLogPayload):
                # Process each entry in the payload
                for entry in payload.entries:
                    timestamp = entry.timestamp.strftime("%H:%M:%S")
                    formatted_message = f"[{timestamp}] {entry.message}"

                    self.log_message(f"Crawler: {formatted_message}")

                    # Route to appropriate ActivityMonitor tab based on activity type
                    if hasattr(self, 'activity_monitor'):
                        if entry.activity_type and 'ai' in str(entry.activity_type).lower():
                            self.activity_monitor.log_ai_decision(formatted_message)
                        else:
                            self.activity_monitor.log_live_browser(formatted_message)
            else:
                # Handle other payload types (fallback for backward compatibility)
                message = payload.message if hasattr(payload, 'message') else f"Unknown payload type: {type(payload).__name__}"
                activity_type = payload.activity_type if hasattr(payload, 'activity_type') else None

                self.log_message(f"Crawler: {message}")

                # Route to appropriate ActivityMonitor tab based on activity type
                if hasattr(self, 'activity_monitor'):
                    if activity_type and 'ai' in str(activity_type).lower():
                        self.activity_monitor.log_ai_decision(message)
                    else:
                        self.activity_monitor.log_live_browser(message)

        except Exception as e:
            self.log_message(f"Error handling activity log: {e}")

    def handle_crawler_error(self, payload):
        """Handle error events from the crawler service."""
        try:
            error_msg = payload.message if hasattr(payload, 'message') else str(payload)
            should_stop = payload.should_stop if hasattr(payload, 'should_stop') else False

            self.log_message(f"Crawler ERROR: {error_msg}")

            # Log to ActivityMonitor error tab
            if hasattr(self, 'activity_monitor'):
                self.activity_monitor.log_error(error_msg)

            # Stop crawling if it's a critical error
            if should_stop:
                self.is_crawling = False
                self.update_status("Error - Stopped")

        except Exception as e:
            self.log_message(f"Error handling crawler error: {e}")
    

    
    # Handle status updates
    def handle_status_update(self, payload):
        if not isinstance(payload, StatusUpdatePayload):
            return
        
        self.pending_updates['status_updates'].append(payload)

    # Handle page processed events
    def handle_page_processed(self, payload):
        if not isinstance(payload, PageProcessedPayload):
            return

        self.pending_updates['page_processed'].append(payload)

    # Perform throttled updates
    def perform_throttled_update(self):
        """Update the status bar and progress widgets at a throttled pace."""
        if self.pending_updates['status_updates']:
            latest_status = self.pending_updates['status_updates'][-1]
            # Update operation status, memory, and elapsed time
            operation = latest_status.status.value.capitalize()
            self.update_status_bar(operation=operation, pages_crawled=latest_status.statistics.pages_crawled)
        
        if self.pending_updates['page_processed']:
            latest_page = self.pending_updates['page_processed'][-1]
            # Update tool and overview progress
            pages_crawled = latest_page.page_content.word_count  # Example use
            total_pages = self.pending_updates['status_updates'][-1].statistics.urls_discovered if self.pending_updates['status_updates'] else 0
            progress = int((pages_crawled / total_pages) * 100) if total_pages > 0 else 0
            self.tool_progress_bar.setValue(progress)

        # Clear pending updates
        self.pending_updates['status_updates'].clear()
        self.pending_updates['page_processed'].clear()
    def on_new_session(self):
        self.log_message("New session created")
        self.on_reset()
    
    def on_save_session(self):
        """Handle save session action."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Session As",
                "session.json",
                "JSON Files (*.json);;All Files (*)"
            )

            if not file_path:
                return

            # Get configurations and create session data
            session_data = self._get_current_session_data()
            
            # Create session export data with version info
            export_data = {
                "export_metadata": {
                    "export_timestamp": datetime.now().isoformat(),
                    "exporter_version": "1.0",
                    "data_format": "json",
                    "session_format_version": "2.0"
                },
                "session_data": session_data
            }

            # Write session file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            self._mark_session_saved()
            self.log_message(f"Session saved to {file_path}")
            QMessageBox.information(self, "Save Successful", "Session saved successfully.")
            
        except Exception as e:
            self.log_message(f"Error saving session: {e}")
            QMessageBox.critical(self, "Save Error", f"An error occurred during saving:\n{str(e)}")
    
    def on_open_session(self):
        """Handle open session action."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Open Session",
                "",
                "JSON Files (*.json);;All Files (*)"
            )

            if not file_path:
                return

            with open(file_path, 'r', encoding='utf-8') as f:
                session_file = json.load(f)

            # Check version compatibility
            metadata = session_file.get('export_metadata', {})
            session_version = metadata.get('session_format_version', '1.0')
            
            if not self._is_version_compatible(session_version):
                QMessageBox.warning(
                    self, 
                    "Version Incompatibility", 
                    f"The session file version ({session_version}) is not compatible with this version of Crawler9000.\n\n"
                    "Please use a compatible version or export a new session."
                )
                return

            # Load session data
            session_data = session_file.get('session_data', {})
            if self._load_session_data(session_data):
                self._mark_session_saved()
                self.log_message(f"Session loaded from {file_path}")
                QMessageBox.information(self, "Open Successful", "Session loaded successfully.")
            else:
                QMessageBox.warning(self, "Load Failed", "Failed to load session data.")
                
        except FileNotFoundError:
            QMessageBox.critical(self, "File Not Found", "The selected session file could not be found.")
        except json.JSONDecodeError:
            QMessageBox.critical(self, "Invalid File Format", "The selected file is not a valid session file.")
        except Exception as e:
            self.log_message(f"Error loading session: {e}")
            QMessageBox.critical(self, "Open Error", f"An error occurred during loading:\n{str(e)}")
    
    def show_help(self):
        """Display comprehensive help dialog with keyboard shortcuts."""
        help_dialog = KeyboardReferenceDialog(self)
        help_dialog.exec()
    
    def on_about(self):
        self.log_message("About dialog - Feature not yet implemented")
    
    def get_current_statistics(self) -> CrawlStatistics:
        """Get current session statistics from real crawler data."""
        from datetime import datetime

        # Return real statistics if available, otherwise create default
        if self.current_statistics:
            return self.current_statistics

        # Create default statistics when no crawling has occurred
        return CrawlStatistics(
            session_id=self.current_session_id or "no-session",
            start_time=datetime.now(),
            status=CrawlStatus.IDLE,
            urls_discovered=len(self.crawled_urls),
            urls_processed=len(self.crawled_urls),
            pages_crawled=len(self.crawled_content)
        )
    
    def get_session_data(self) -> dict:
        """Get complete session data for export using real crawler data."""
        statistics = self.get_current_statistics()

        # Get real crawler data from the crawler service if available
        real_urls = self.crawled_urls.copy()
        real_content = self.crawled_content.copy()

        # Get activity logs from ActivityMonitor if available
        activity_logs = []
        errors = []
        if hasattr(self, 'activity_monitor'):
            try:
                # Extract logs from ActivityMonitor's buffer
                activity_logs = self.activity_monitor.get_all_logs()
                errors = self.activity_monitor.get_error_logs()
            except Exception as e:
                self.log_message(f"Warning: Could not retrieve activity logs: {e}")

        return {
            'statistics': statistics,
            'urls': real_urls,
            'page_content': real_content,
            'activity_logs': activity_logs,
            'errors': errors,
            'ai_analysis': []  # TODO: Add real AI analysis data when available
        }

    def on_export_json(self):
        """Export session data to JSON format."""
        try:
            # Get file path from user
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Session as JSON",
                "session_export.json",
                "JSON Files (*.json);;All Files (*)"
            )
            
            if not file_path:
                return
            
            # Get session data and export
            session_data = self.get_session_data()
            exporter = SessionExporter(session_data)
            
            if exporter.export_to_json(file_path):
                self.log_message(f"Session exported as JSON to {file_path}")
                QMessageBox.information(self, "Export Successful", 
                                       f"Session data exported successfully to {file_path}")
            else:
                self.log_message("Failed to export session as JSON")
                QMessageBox.warning(self, "Export Failed", 
                                   "Failed to export session data to JSON")
                
        except Exception as e:
            self.log_message(f"Error exporting JSON: {e}")
            QMessageBox.critical(self, "Export Error", f"Error exporting session: {e}")

    def on_export_csv(self):
        """Export session data to CSV format."""
        try:
            # Get directory from user
            dir_path = QFileDialog.getExistingDirectory(
                self,
                "Select Directory for CSV Export",
                "."
            )
            
            if not dir_path:
                return
            
            # Get session data and export
            session_data = self.get_session_data()
            exporter = SessionExporter(session_data)
            
            if exporter.export_to_csv(dir_path):
                self.log_message(f"Session exported as CSV to {dir_path}")
                QMessageBox.information(self, "Export Successful", 
                                       f"Session data exported successfully to {dir_path}")
            else:
                self.log_message("Failed to export session as CSV")
                QMessageBox.warning(self, "Export Failed", 
                                   "Failed to export session data to CSV")
                
        except Exception as e:
            self.log_message(f"Error exporting CSV: {e}")
            QMessageBox.critical(self, "Export Error", f"Error exporting session: {e}")
    
    # Session management helper methods
    def _get_current_session_data(self) -> dict:
        """Get current session data including configurations and state."""
        try:
            # Get current application config
            app_config = get_config()
            
            # Get current GUI configuration from the configuration panel
            current_gui_config = self.get_current_config()
            
            # Build session data structure
            session_data = {
                "crawler_config": {
                    "start_url": current_gui_config.get("url", ""),
                    "crawl_mode": current_gui_config.get("crawl_mode", "research"),
                    "max_pages": int(current_gui_config["advanced"]["max_pages"]) if current_gui_config["advanced"]["max_pages"] else 100,
                    "max_depth": int(current_gui_config["advanced"]["depth"]) if current_gui_config["advanced"]["depth"] else 3,
                    "follow_external_links": current_gui_config["advanced"]["follow_external"],
                    "respect_robots_txt": current_gui_config["advanced"]["respect_robots"],
                    "allowed_domains": current_gui_config["advanced"]["allow_domains"].split(",") if current_gui_config["advanced"]["allow_domains"] else []
                },
                "ai_config": {
                    "intelligence_level": current_gui_config.get("intelligence_level", 3),
                    "enable_content_analysis": True,
                    "enable_link_analysis": True,
                    "enable_smart_filtering": True
                },
                "gui_config": {
                    "window_width": self.width(),
                    "window_height": self.height(),
                    "theme": app_config.gui.theme,
                    "auto_scroll_logs": app_config.gui.auto_scroll_logs,
                    "show_timestamps": app_config.gui.show_timestamps
                },
                "session_state": {
                    "current_session_id": self.current_session_id,
                    "is_crawling": getattr(self, 'is_crawling', False),
                    "current_tab": self.central_tabs.currentIndex(),
                    "crawled_urls_count": len(self.crawled_urls),
                    "crawled_content_count": len(self.crawled_content)
                }
            }
            
            return session_data
        except Exception as e:
            logger.error(f"Error getting current session data: {e}")
            return {}
    
    def _load_session_data(self, session_data: dict) -> bool:
        """Load session data and update configurations."""
        try:
            # Update crawler configuration
            if "crawler_config" in session_data:
                crawler_config = session_data["crawler_config"]
                
                # Update configuration panel with loaded data
                if hasattr(self, 'configuration_panel'):
                    # This would need to be implemented in the configuration panel
                    # For now, we'll just log what we would load
                    self.log_message(f"Loading crawler config: {crawler_config.get('start_url', 'N/A')}")
            
            # Update AI configuration
            if "ai_config" in session_data:
                ai_config = session_data["ai_config"]
                self.log_message(f"Loading AI config: Intelligence level {ai_config.get('intelligence_level', 3)}")
            
            # Update GUI configuration
            if "gui_config" in session_data:
                gui_config = session_data["gui_config"]
                if "window_width" in gui_config and "window_height" in gui_config:
                    self.resize(gui_config["window_width"], gui_config["window_height"])
            
            # Restore session state
            if "session_state" in session_data:
                state = session_data["session_state"]
                if "current_tab" in state:
                    self.central_tabs.setCurrentIndex(state["current_tab"])
            
            return True
        except Exception as e:
            logger.error(f"Error loading session data: {e}")
            return False
    
    def _is_version_compatible(self, session_version: str) -> bool:
        """Check if the session file version is compatible with the current application version."""
        # Define compatible versions
        compatible_versions = ["1.0", "2.0"]
        return session_version in compatible_versions
    
    def _mark_session_saved(self):
        """Mark the session as saved (no unsaved changes)."""
        self.session_saved = True
        # Update window title to remove unsaved indicator
        title = self.windowTitle()
        if title.endswith(" *"):
            self.setWindowTitle(title[:-2])
    
    def _mark_session_changed(self):
        """Mark the session as having unsaved changes."""
        if self.session_saved:
            self.session_saved = False
            # Update window title to show unsaved indicator
            title = self.windowTitle()
            if not title.endswith(" *"):
                self.setWindowTitle(title + " *")
    
    def _auto_save_session(self):
        """Auto-save the current session to a temporary location."""
        try:
            import tempfile
            import os
            
            # Create auto-save directory if it doesn't exist
            auto_save_dir = os.path.join(tempfile.gettempdir(), "crawler9000_autosave")
            os.makedirs(auto_save_dir, exist_ok=True)
            
            # Generate auto-save filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            auto_save_path = os.path.join(auto_save_dir, f"autosave_{timestamp}.json")
            
            # Get current session data
            session_data = self._get_current_session_data()
            
            # Create export data with version info
            export_data = {
                "export_metadata": {
                    "export_timestamp": datetime.now().isoformat(),
                    "exporter_version": "1.0",
                    "data_format": "json",
                    "session_format_version": "2.0",
                    "is_auto_save": True
                },
                "session_data": session_data
            }
            
            # Write auto-save file
            with open(auto_save_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            self.last_auto_save_path = auto_save_path
            logger.info(f"Session auto-saved to {auto_save_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error during auto-save: {e}")
            return False
    
    def _prompt_save_changes(self) -> bool:
        """Prompt user to save unsaved changes. Returns True if it's safe to continue."""
        if self.session_saved:
            return True
        
        reply = QMessageBox.question(
            self,
            "Unsaved Changes",
            "You have unsaved changes in your session. Do you want to save them before closing?",
            QMessageBox.StandardButton.Save | 
            QMessageBox.StandardButton.Discard | 
            QMessageBox.StandardButton.Cancel,
            QMessageBox.StandardButton.Save
        )
        
        if reply == QMessageBox.StandardButton.Save:
            # Attempt to save
            self.on_save_session()
            return self.session_saved  # Only continue if save was successful
        elif reply == QMessageBox.StandardButton.Discard:
            return True  # Continue without saving
        else:  # Cancel
            return False  # Don't continue
    
    def on_settings(self):
        """Handle settings action - placeholder for now."""
        self.log_message("Settings dialog - Feature not yet implemented")
    
    def closeEvent(self, event):
        """Handle window close event with auto-save and unsaved changes prompt."""
        try:
            # Check for unsaved changes and prompt user
            if not self._prompt_save_changes():
                event.ignore()  # User cancelled, don't close
                return
            
            # Perform auto-save of last session
            self._auto_save_session()
            
            # Save window and dock geometry to config
            self.save_layout_geometry()
            
            logger.info("MainWindow closed gracefully with auto-save")
            event.accept()
            
        except Exception as e:
            logger.error(f"Error during close event: {e}")
            # Still allow closing even if there's an error
            event.accept()

