"""
ThemeManager: Manages the application's theme settings using Qt Style Sheets.

This class allows switching between light, dark, and custom themes.
"""
from PyQt6.QtWidgets import QApplication

class ThemeManager:
    def __init__(self, default_theme='system'):
        self.current_theme = default_theme
        self.stylesheets = {
            'system': self._get_system_theme(),
            'light': self._get_light_theme(),
            'dark': self._get_dark_theme(),
            'custom': self._get_custom_theme()
        }

    def apply_theme(self, theme_name: str):
        """Apply the selected theme to the application."""
        if theme_name in self.stylesheets:
            self.current_theme = theme_name
            QApplication.instance().setStyleSheet(self.stylesheets[theme_name])

    def save_current_theme(self, config):
        """Save the current theme to configuration."""
        config.gui.theme = self.current_theme
    
    def _get_system_theme(self):
        """Get system theme (follows OS theme)."""
        return ""
    
    def _get_light_theme(self):
        """Get light theme stylesheet."""
        return """
        QMainWindow {
            background-color: #ffffff;
            color: #333333;
        }
        
        QMenuBar {
            background-color: #f5f5f5;
            color: #333333;
            border-bottom: 1px solid #cccccc;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #e3f2fd;
            border-radius: 4px;
        }
        
        QMenu {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #e3f2fd;
        }
        
        QToolBar {
            background-color: #f5f5f5;
            border: 1px solid #cccccc;
            spacing: 3px;
        }
        
        QToolBar::separator {
            background-color: #cccccc;
            width: 1px;
            margin: 4px;
        }
        
        QPushButton {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 8px 16px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #f0f0f0;
            border-color: #999999;
        }
        
        QPushButton:pressed {
            background-color: #e0e0e0;
        }
        
        QPushButton:disabled {
            background-color: #f5f5f5;
            color: #cccccc;
            border-color: #e0e0e0;
        }
        
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 4px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #2196f3;
        }
        
        QComboBox {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 4px 8px;
            min-width: 100px;
        }
        
        QComboBox:hover {
            border-color: #999999;
        }
        
        QComboBox::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 20px;
            border-left: 1px solid #cccccc;
        }
        
        QComboBox::down-arrow {
            image: url(down_arrow.png);
            width: 10px;
            height: 10px;
        }
        
        QComboBox QAbstractItemView {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            selection-background-color: #e3f2fd;
        }
        
        QSpinBox {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 4px;
        }
        
        QSpinBox:focus {
            border-color: #2196f3;
        }
        
        QCheckBox {
            color: #333333;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: #ffffff;
        }
        
        QCheckBox::indicator:checked {
            background-color: #2196f3;
            border-color: #2196f3;
        }
        
        QGroupBox {
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 8px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px;
            background-color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }
        
        QTabBar::tab {
            background-color: #f5f5f5;
            color: #333333;
            border: 1px solid #cccccc;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #ffffff;
            border-bottom-color: #ffffff;
        }
        
        QTabBar::tab:hover {
            background-color: #e0e0e0;
        }
        
        QDockWidget {
            color: #333333;
            titlebar-close-icon: url(close.png);
            titlebar-normal-icon: url(undock.png);
        }
        
        QDockWidget::title {
            background-color: #f5f5f5;
            padding: 4px;
            border-bottom: 1px solid #cccccc;
        }
        
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 4px;
            text-align: center;
            background-color: #f5f5f5;
        }
        
        QProgressBar::chunk {
            background-color: #4caf50;
            border-radius: 3px;
        }
        
        QStatusBar {
            background-color: #f5f5f5;
            color: #333333;
            border-top: 1px solid #cccccc;
        }
        
        QStatusBar QLabel {
            padding: 2px 8px;
        }
        
        QListWidget {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #cccccc;
            border-radius: 4px;
            alternate-background-color: #f9f9f9;
        }
        
        QListWidget::item {
            padding: 4px;
            border-bottom: 1px solid #eeeeee;
        }
        
        QListWidget::item:selected {
            background-color: #e3f2fd;
            color: #333333;
        }
        
        QScrollBar:vertical {
            background-color: #f5f5f5;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #999999;
        }
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        
        QScrollBar:horizontal {
            background-color: #f5f5f5;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #cccccc;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #999999;
        }
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
            width: 0px;
        }
        """
    
    def _get_dark_theme(self):
        """Get dark theme stylesheet."""
        return """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QMenuBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-bottom: 1px solid #555555;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #0d7377;
            border-radius: 4px;
        }
        
        QMenu {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #0d7377;
        }
        
        QToolBar {
            background-color: #3c3c3c;
            border: 1px solid #555555;
            spacing: 3px;
        }
        
        QToolBar::separator {
            background-color: #555555;
            width: 1px;
            margin: 4px;
        }
        
        QPushButton {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 8px 16px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #4a4a4a;
            border-color: #777777;
        }
        
        QPushButton:pressed {
            background-color: #2a2a2a;
        }
        
        QPushButton:disabled {
            background-color: #2b2b2b;
            color: #666666;
            border-color: #444444;
        }
        
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #0d7377;
        }
        
        QComboBox {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px 8px;
            min-width: 100px;
        }
        
        QComboBox:hover {
            border-color: #777777;
        }
        
        QComboBox::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 20px;
            border-left: 1px solid #555555;
        }
        
        QComboBox::down-arrow {
            image: url(down_arrow_dark.png);
            width: 10px;
            height: 10px;
        }
        
        QComboBox QAbstractItemView {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            selection-background-color: #0d7377;
        }
        
        QSpinBox {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px;
        }
        
        QSpinBox:focus {
            border-color: #0d7377;
        }
        
        QCheckBox {
            color: #ffffff;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #555555;
            border-radius: 3px;
            background-color: #3c3c3c;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0d7377;
            border-color: #0d7377;
        }
        
        QGroupBox {
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 8px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px;
            background-color: #2b2b2b;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #2b2b2b;
            border-bottom-color: #2b2b2b;
        }
        
        QTabBar::tab:hover {
            background-color: #4a4a4a;
        }
        
        QDockWidget {
            color: #ffffff;
            titlebar-close-icon: url(close_dark.png);
            titlebar-normal-icon: url(undock_dark.png);
        }
        
        QDockWidget::title {
            background-color: #3c3c3c;
            padding: 4px;
            border-bottom: 1px solid #555555;
        }
        
        QProgressBar {
            border: 1px solid #555555;
            border-radius: 4px;
            text-align: center;
            background-color: #3c3c3c;
            color: #ffffff;
        }
        
        QProgressBar::chunk {
            background-color: #4caf50;
            border-radius: 3px;
        }
        
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #555555;
        }
        
        QStatusBar QLabel {
            padding: 2px 8px;
        }
        
        QListWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            alternate-background-color: #353535;
        }
        
        QListWidget::item {
            padding: 4px;
            border-bottom: 1px solid #444444;
        }
        
        QListWidget::item:selected {
            background-color: #0d7377;
            color: #ffffff;
        }
        
        QScrollBar:vertical {
            background-color: #3c3c3c;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #666666;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #888888;
        }
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        
        QScrollBar:horizontal {
            background-color: #3c3c3c;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #666666;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #888888;
        }
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
            width: 0px;
        }
        """
    
    def _get_custom_theme(self):
        """Get custom theme stylesheet - can be loaded from file or database."""
        # For demonstration, return a custom blue theme
        return """
        QMainWindow {
            background-color: #e8f4f8;
            color: #1a1a1a;
        }
        
        QMenuBar {
            background-color: #d1ecf1;
            color: #1a1a1a;
            border-bottom: 1px solid #bee5eb;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #17a2b8;
            color: #ffffff;
            border-radius: 4px;
        }
        
        QPushButton {
            background-color: #ffffff;
            color: #1a1a1a;
            border: 1px solid #17a2b8;
            border-radius: 4px;
            padding: 8px 16px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #17a2b8;
            color: #ffffff;
        }
        
        QPushButton:pressed {
            background-color: #138496;
        }
        
        QTabBar::tab:selected {
            background-color: #17a2b8;
            color: #ffffff;
        }
        
        QDockWidget::title {
            background-color: #d1ecf1;
            padding: 4px;
            border-bottom: 1px solid #bee5eb;
        }
        """
