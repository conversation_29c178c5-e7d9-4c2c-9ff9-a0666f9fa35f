"""
StatisticsDock: A PyQt6 Dockable Component for Crawling Statistics Visualization

This module defines a dockable statistics visualization component
that provides real-time graphing of crawling metrics using PyQtGraph and Matplotlib.

Features:
- Live line chart for pages per minute
- Pie chart of HTTP status codes  
- Gauge for success rate (using QProgressBar)
- Export functionality to PNG/SVG
- Real-time updates via SignalBus
- Thread-safe data aggregation
- Professional styling and layout

Data Source:
- Aggregated CrawlStatistics via SignalBus
- Updates in real-time as signals arrive
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from collections import deque, defaultdict

from PyQt6.QtWidgets import (
    QDockWidget, QVBoxLayout, QHBoxLayout, QWidget, QLabel, 
    QPushButton, QProgressBar, QGroupBox, QFileDialog,
    QMessageBox, QFrame, QSizePolicy, QComboBox
)
from PyQt6.QtCore import Qt, QSize, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor

try:
    import pyqtgraph as pg
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    HAS_PLOTTING = True
except ImportError:
    HAS_PLOTTING = False
    pg = None
    plt = None
    FigureCanvas = None
    Figure = None
    np = None

from ..shared.models import (
    CrawlStatistics, StatusUpdatePayload, PageProcessedPayload,
    CrawlStatus, HttpStatusCategory, StatisticsPayload
)
from ..shared.signal_bus import (
    SignalType, connect_signal, register_component
)

logger = logging.getLogger(__name__)


class StatisticsDock(QDockWidget):
    """
    A dock widget for displaying statistics about the crawling session.
    """

    COMPONENT_ID = "statistics_dock"

    def __init__(self, parent=None):
        super().__init__("Statistics", parent)
        self.setObjectName("StatisticsDock")

        # Register with SignalBus
        register_component(self.COMPONENT_ID, self)

        # Initialize the main widget
        main_widget = QWidget(self)

        # Create a vertical layout for all components
        layout = QVBoxLayout()

        # Line chart for pages per minute (if plotting available)
        if HAS_PLOTTING and pg:
            self.pages_per_min_chart = pg.PlotWidget()
            self.pages_per_min_chart.setTitle("Pages per Minute")
            self.pages_curve = self.pages_per_min_chart.plot(pen=pg.mkPen('b', width=2))
            self.pages_data = []  # Store pages/minute data
            layout.addWidget(self.pages_per_min_chart)
        else:
            # Fallback: simple label when plotting not available
            self.pages_per_min_chart = None
            self.pages_curve = None
            self.pages_data = []
            fallback_label = QLabel("Charts require pyqtgraph and matplotlib")
            fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(fallback_label)

        # Pie chart for HTTP status codes (if plotting available)
        if HAS_PLOTTING and plt:
            self.status_fig, self.status_ax = plt.subplots()
            self.status_canvas = FigureCanvas(self.status_fig)
            layout.addWidget(self.status_canvas)
        else:
            self.status_fig = None
            self.status_ax = None
            self.status_canvas = None

        # Gauge for success rate
        self.success_gauge = QProgressBar()
        self.success_gauge.setRange(0, 100)
        self.success_gauge.setFormat("Success Rate: %p%")
        layout.addWidget(self.success_gauge)

        # Export buttons
        export_btn_layout = QVBoxLayout()

        self.export_png_btn = QPushButton("Export as PNG")
        self.export_png_btn.clicked.connect(self.export_as_png)
        self.export_png_btn.setToolTip("Export charts as PNG image")
        self.export_png_btn.setStatusTip("Save current statistics charts as PNG file")
        export_btn_layout.addWidget(self.export_png_btn)

        self.export_svg_btn = QPushButton("Export as SVG")
        self.export_svg_btn.clicked.connect(self.export_as_svg)
        self.export_svg_btn.setToolTip("Export charts as SVG vector image")
        self.export_svg_btn.setStatusTip("Save current statistics charts as scalable SVG file")
        export_btn_layout.addWidget(self.export_svg_btn)

        layout.addLayout(export_btn_layout)

        main_widget.setLayout(layout)
        self.setWidget(main_widget)

        # Connect to statistics update signals
        self.setStatusTip("Real-time statistics and visualizations")
        connect_signal(SignalType.STATUS_UPDATE, self.update_statistics, "statistics_dock")

    def update_statistics(self, payload: StatisticsPayload):
        """
        Process incoming statistics payloads.
        """
        stats: CrawlStatistics = payload.statistics

        self.update_pages_per_min_chart(stats)
        self.update_status_pie_chart(stats)
        self.update_success_gauge(stats)

    def update_pages_per_min_chart(self, stats: CrawlStatistics):
        """
        Update the pages per minute line chart.
        """
        if self.pages_curve is not None:
            pages_per_min = (stats.pages_crawled / ((stats.end_time or stats.start_time) - stats.start_time).seconds) * 60
            self.pages_data.append(pages_per_min)
            self.pages_curve.setData(self.pages_data)

    def update_status_pie_chart(self, stats: CrawlStatistics):
        """
        Update the HTTP status codes pie chart.
        """
        if self.status_ax is not None and self.status_canvas is not None:
            status_counts = [stats.urls_processed, stats.urls_failed]
            self.status_ax.clear()
            self.status_ax.pie(status_counts, labels=["Processed", "Failed"], autopct='%1.1f%%', startangle=90)
            self.status_ax.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle.
            self.status_canvas.draw()

    def update_success_gauge(self, stats: CrawlStatistics):
        """
        Update the success rate gauge.
        """
        success_rate = (stats.urls_processed / (stats.urls_processed + stats.urls_failed)) * 100 if (stats.urls_processed + stats.urls_failed) > 0 else 0
        self.success_gauge.setValue(success_rate)

    def export_as_png(self):
        """
        Export the current visualizations to a PNG file.
        """
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Statistics as PNG", "statistics_export.png", "PNG Files (*.png);;All Files (*)")
        if file_path:
            self.status_fig.savefig(file_path, format='png')

    def export_as_svg(self):
        """
        Export the current visualizations to an SVG file.
        """
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Statistics as SVG", "statistics_export.svg", "SVG Files (*.svg);;All Files (*)")
        if file_path:
            self.status_fig.savefig(file_path, format='svg')

