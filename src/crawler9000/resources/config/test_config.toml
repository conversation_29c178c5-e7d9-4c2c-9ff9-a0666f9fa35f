# Test configuration for Crawler9000
# Optimized for fast testing without external dependencies

[crawler]
max_pages = 5
max_depth = 2
delay_between_requests = 0.1
timeout = 10
follow_external_links = false
respect_robots_txt = false  # Disabled for testing
allowed_domains = []
blocked_domains = []
max_file_size_mb = 5
allowed_file_types = ["html", "htm", "txt"]

[ai]
api_provider = "deepseek"
api_key = ""  # No API key for testing
api_base_url = "https://api.deepseek.com"
model_name = "deepseek-chat"
intelligence_level = 3
max_tokens = 1000
temperature = 0.7
# Rate limiting settings (more restrictive for testing)
max_requests_per_min = 30
retry_max_attempts = 2
retry_backoff_factor = 1.5
# Analysis settings
enable_content_analysis = false  # Disabled for testing
enable_link_analysis = false    # Disabled for testing
enable_sentiment_analysis = false

[gui]
window_width = 1200
window_height = 800
remember_window_state = true
theme = "system"
enable_animations = true
left_panel_width = 300
right_panel_width = 400
auto_scroll_logs = true
max_log_entries = 500
show_timestamps = true

[proxy]
enable_proxy = false
proxy_type = "http"
proxy_host = ""
proxy_port = 8080
proxy_username = ""
proxy_password = ""
enable_rotation = false
proxy_list = []

# Application-level settings
log_level = "INFO"
log_to_file = false  # Disabled for testing
log_file_path = "logs/test_crawler.log"
data_directory = "test_data"
export_directory = "test_exports"
temp_directory = "test_temp"
