def handle_unhandled_exception(exc_type, exc_value, exc_traceback):
    """Global exception handler for unhandled exceptions."""
    import traceback
    from PyQt6.QtWidgets import QMessageBox
    from crawler9000.shared.models import ErrorPayload, ErrorInfo
    from crawler9000.shared.signal_bus import SignalType, emit_signal
    
    try:
        # Log the exception to ActivityMonitor
        error_info = ErrorInfo(
            error_type='UNHANDLED_EXCEPTION',
            message=str(exc_value),
            stack_trace=''.join(traceback.format_tb(exc_traceback)),
            url=None,
            is_recoverable=False
        )
        error_payload = ErrorPayload(error=error_info)
        
        # Try to emit the error signal
        try:
            emit_signal(SignalType.ERROR_OCCURRED, error_payload)
        except Exception:
            # If signal bus is not available, log to console
            logger.error(f"Failed to emit error signal: {exc_value}", exc_info=True)
        
        # Show QMessageBox with retry/cancel
        msg_box = QMessageBox()
        msg_box.setWindowTitle("Application Error")
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setText("An unexpected error occurred.")
        msg_box.setInformativeText(f"Error: {str(exc_value)}")
        msg_box.setDetailedText(''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
        msg_box.setStandardButtons(QMessageBox.StandardButton.Retry | QMessageBox.StandardButton.Close)
        msg_box.setDefaultButton(QMessageBox.StandardButton.Close)
        
        response = msg_box.exec()
        if response == QMessageBox.StandardButton.Retry:
            # For now, just restart the application
            # This could be enhanced to retry specific operations
            logger.info("User chose to retry after unhandled exception")
            return
        else:
            logger.info("User chose to exit after unhandled exception")
            sys.exit(1)
            
    except Exception as e:
        # Fallback error handling if our error handler itself fails
        print(f"Fatal error in exception handler: {e}")
        print(f"Original exception: {exc_value}")
        sys.exit(1)


"""
Main entry point for Crawler9000

This module initializes the application and starts the PyQt6 GUI.
"""

import sys
import logging
from pathlib import Path

# Add src directory to Python path for development
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

from crawler9000.utils.logger import setup_logging
from crawler9000.utils.config import get_config
from crawler9000.gui.main_window import MainWindow

logger = logging.getLogger(__name__)


def main():
    """Main application entry point."""
    # Set up logging first
    setup_logging()
    logger.info("Starting Crawler9000...")
    
    # Load configuration
    config = get_config()
    logger.info("Configuration loaded")
    
    # Create Qt application
    app = QApplication(sys.argv)
    app.setApplicationName("Crawler9000")
    app.setApplicationVersion("0.1.0")
    app.setOrganizationName("robotcowboy808")
    
    # Enable high DPI support (handled automatically in Qt6)
    # High DPI scaling is enabled by default in Qt6
    
    # Create and show main window
    main_window = MainWindow()
    main_window.show()
    
    logger.info("Crawler9000 GUI started")

    # Set global exception handler
    sys.excepthook = handle_unhandled_exception

    # Run the application
    exit_code = app.exec()
    logger.info(f"Application exited with code: {exit_code}")
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
