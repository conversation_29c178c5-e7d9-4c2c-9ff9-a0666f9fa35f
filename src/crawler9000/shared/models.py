"""
Shared data models for frontend-backend communication in Crawler9000.

This module defines all data classes, enums, and structures that are shared
between the GUI (frontend) and the core crawler engine (backend) to ensure
consistent data exchange and type safety.

All DTOs are wrapped with Pydantic BaseModel for runtime validation while
maintaining dataclass compatibility for existing code.
"""

from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Any, Set, Union
from enum import Enum, auto
from datetime import datetime
import uuid
import weakref
import psutil
import json
import csv
import io

# Pydantic models are defined in separate pydantic_models.py to avoid import issues


# =============================================================================
# Enums for Status and Configuration
# =============================================================================

class CrawlStatus(Enum):
    """Enumeration of possible crawler states."""
    IDLE = "idle"
    STARTING = "starting"
    CRAWLING = "crawling"
    PAUSED = "paused"
    STOPPING = "stopping"
    COMPLETED = "completed"
    ERROR = "error"


class CrawlMode(Enum):
    """Enumeration of crawl modes."""
    DOCUMENTATION = "documentation"
    RESEARCH = "research"
    TARGETED = "targeted"
    CUSTOM = "custom"


class LogLevel(Enum):
    """Log levels for activity monitoring."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AIDecisionType(Enum):
    """Types of AI decisions."""
    URL_PRIORITY = "url_priority"
    CONTENT_ANALYSIS = "content_analysis"
    CRAWL_STRATEGY = "crawl_strategy"
    LINK_FILTERING = "link_filtering"
    ERROR_RECOVERY = "error_recovery"


class ActivityType(Enum):
    """Types of crawler activities for monitoring."""
    PAGE_REQUEST = "page_request"
    PAGE_PARSED = "page_parsed"
    LINK_DISCOVERED = "link_discovered"
    AI_DECISION = "ai_decision"
    ERROR_OCCURRED = "error_occurred"
    STATUS_UPDATE = "status_update"
    RATE_LIMIT = "rate_limit"
    SESSION_STARTED = "session_started"
    SESSION_COMPLETED = "session_completed"
    QUEUE_EMPTY = "queue_empty"
    DEPTH_LIMIT_REACHED = "depth_limit_reached"


class CrawlEvent(Enum):
    """Crawl events for fine-grained monitoring."""
    URL_QUEUED = "url_queued"
    URL_DEQUEUED = "url_dequeued"
    PAGE_FETCH_START = "page_fetch_start"
    PAGE_FETCH_SUCCESS = "page_fetch_success"
    PAGE_FETCH_FAILED = "page_fetch_failed"
    CONTENT_PARSE_START = "content_parse_start"
    CONTENT_PARSE_SUCCESS = "content_parse_success"
    CONTENT_PARSE_FAILED = "content_parse_failed"
    AI_ANALYSIS_START = "ai_analysis_start"
    AI_ANALYSIS_SUCCESS = "ai_analysis_success"
    AI_ANALYSIS_FAILED = "ai_analysis_failed"
    LINKS_EXTRACTED = "links_extracted"
    LINKS_FILTERED = "links_filtered"
    PRIORITY_CALCULATED = "priority_calculated"
    ROBOTS_TXT_CHECKED = "robots_txt_checked"
    RATE_LIMIT_APPLIED = "rate_limit_applied"
    TIMEOUT_OCCURRED = "timeout_occurred"
    REDIRECT_FOLLOWED = "redirect_followed"
    CONTENT_TYPE_FILTERED = "content_type_filtered"
    FILE_SIZE_LIMIT_EXCEEDED = "file_size_limit_exceeded"


class ContentType(Enum):
    """Content types for classification."""
    HTML = "text/html"
    PLAIN_TEXT = "text/plain"
    JSON = "application/json"
    XML = "application/xml"
    PDF = "application/pdf"
    IMAGE = "image/*"
    VIDEO = "video/*"
    AUDIO = "audio/*"
    UNKNOWN = "unknown"


class HttpStatusCategory(Enum):
    """HTTP status code categories."""
    SUCCESS = "2xx"
    REDIRECT = "3xx"
    CLIENT_ERROR = "4xx"
    SERVER_ERROR = "5xx"
    UNKNOWN = "unknown"


# =============================================================================
# Configuration Data Classes
# =============================================================================

@dataclass
class CrawlerConfig:
    """Core crawler configuration."""
    start_url: str
    crawl_mode: CrawlMode = CrawlMode.RESEARCH
    max_pages: int = 100
    max_depth: int = 3
    delay_between_requests: float = 1.0
    timeout: int = 30
    follow_external_links: bool = False
    respect_robots_txt: bool = True
    allowed_domains: List[str] = field(default_factory=list)
    max_file_size_mb: int = 10
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))


@dataclass
class AIConfig:
    """AI-related configuration."""
    # API settings
    api_provider: str = "deepseek"
    api_key: str = ""
    api_base_url: str = "https://api.deepseek.com"
    model_name: str = "deepseek-chat"

    # AI behavior settings
    intelligence_level: int = 3  # 1-5 scale
    max_tokens: int = 4000
    temperature: float = 0.7

    # Rate limiting settings
    max_requests_per_min: int = 60
    retry_max_attempts: int = 3
    retry_backoff_factor: float = 2.0

    # Analysis settings
    enable_content_analysis: bool = True
    enable_link_analysis: bool = True
    enable_smart_filtering: bool = True
    enable_sentiment_analysis: bool = False
    content_relevance_threshold: float = 0.5
    decision_confidence_threshold: float = 0.7


@dataclass
class GUIConfig:
    """GUI-specific configuration."""
    auto_scroll: bool = True
    show_debug_logs: bool = False
    update_frequency_ms: int = 1000
    max_log_entries: int = 1000
    theme: str = "default"


# =============================================================================
# Validation Helper Functions
# =============================================================================
# =============================================================================
# Data Transfer Objects (DTOs)
# =============================================================================

@dataclass
class URLData:
    """Represents a discovered URL with metadata."""
    url: str
    title: Optional[str] = None
    description: Optional[str] = None
    depth: int = 0
    parent_url: Optional[str] = None
    discovered_at: datetime = field(default_factory=datetime.now)
    priority: float = 1.0
    is_processed: bool = False
    status_code: Optional[int] = None
    content_type: Optional[str] = None
    content_length: Optional[int] = None

    def update_priority(self, strategy: 'PriorityStrategy'):
        """Update the priority using a given strategy."""
        self.priority = strategy.calculate_priority(self)

    def to_json(self) -> str:
        return json.dumps(asdict(self), default=str)

    @classmethod
    def from_json(cls, data: str) -> 'URLData':
        return cls(**json.loads(data))

    def to_csv(self) -> str:
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=asdict(self).keys())
        writer.writeheader()
        writer.writerow(asdict(self))
        return output.getvalue()


@dataclass
class PageContent:
    """Represents parsed page content."""
    url: str
    title: Optional[str] = None
    text_content: str = ""
    html_content: str = ""
    links: List[URLData] = field(default_factory=list)
    internal_links: List[URLData] = field(default_factory=list)
    external_links: List[URLData] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    word_count: int = 0
    processing_time: float = 0.0
    parsed_at: datetime = field(default_factory=datetime.now)

    def to_json(self) -> str:
        return json.dumps(asdict(self), default=str)

    @classmethod
    def from_json(cls, data: str) -> 'PageContent':
        return cls(**json.loads(data))

    def to_csv(self) -> str:
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=asdict(self).keys())
        writer.writeheader()
        writer.writerow(asdict(self))
        return output.getvalue()


@dataclass
class AIAnalysis:
    """Represents AI analysis results."""
    analysis_type: AIDecisionType
    confidence: float
    decision: str
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class ActivityLogEntry:
    """Represents a single activity log entry."""
    activity_type: ActivityType
    timestamp: datetime = field(default_factory=datetime.now)
    level: LogLevel = LogLevel.INFO
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    url: Optional[str] = None
    session_id: Optional[str] = None


@dataclass
class CrawlStatistics:
    """Represents crawling statistics."""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: CrawlStatus = CrawlStatus.IDLE
    urls_discovered: int = 0
    urls_queued: int = 0
    urls_processed: int = 0
    urls_failed: int = 0
    pages_crawled: int = 0
    total_content_size: int = 0
    average_processing_time: float = 0.0
    errors_count: int = 0
    current_url: Optional[str] = None
    estimated_completion: Optional[datetime] = None

    def to_json(self) -> str:
        return json.dumps(asdict(self), default=str)

    @classmethod
    def from_json(cls, data: str) -> 'CrawlStatistics':
        return cls(**json.loads(data))

    def to_csv(self) -> str:
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=asdict(self).keys())
        writer.writeheader()
        writer.writerow(asdict(self))
        return output.getvalue()


@dataclass
class ErrorInfo:
    """Represents error information."""
    error_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    error_type: str = ""
    message: str = ""
    url: Optional[str] = None
    stack_trace: Optional[str] = None
    recovery_action: Optional[str] = None
    is_recoverable: bool = True
    session_id: Optional[str] = None


# =============================================================================
# Signal Payload Classes
# =============================================================================

@dataclass
class ConfigChangePayload:
    """Payload for configuration change signals."""
    crawler_config: Optional[CrawlerConfig] = None
    ai_config: Optional[AIConfig] = None
    gui_config: Optional[GUIConfig] = None
    changed_fields: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class StatusUpdatePayload:
    """Payload for status update signals."""
    status: CrawlStatus
    statistics: CrawlStatistics
    message: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class PageProcessedPayload:
    """Payload for page processed signals."""
    page_content: PageContent
    ai_analysis: Optional[AIAnalysis] = None
    new_urls: List[URLData] = field(default_factory=list)
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)

    def __str__(self) -> str:
        """Return a formatted string representation of the payload."""
        return f"PageProcessedPayload(url={self.page_content.url}, new_urls={len(self.new_urls)}, processing_time={self.processing_time:.2f}s)"


@dataclass
class ActivityLogPayload:
    """Payload for activity log signals."""
    entries: List[ActivityLogEntry]
    timestamp: datetime = field(default_factory=datetime.now)

    def __str__(self) -> str:
        """Return a formatted string representation of the payload."""
        if not self.entries:
            return f"ActivityLogPayload(empty, timestamp={self.timestamp.strftime('%H:%M:%S')})"

        entry_summaries = []
        for entry in self.entries[:3]:  # Show first 3 entries
            entry_summaries.append(f"{entry.activity_type.value}: {entry.message[:50]}...")

        if len(self.entries) > 3:
            entry_summaries.append(f"... and {len(self.entries) - 3} more entries")

        return f"ActivityLogPayload({len(self.entries)} entries: {'; '.join(entry_summaries)})"


@dataclass
class ErrorPayload:
    """Payload for error signals."""
    error: ErrorInfo
    should_stop: bool = False
    timestamp: datetime = field(default_factory=datetime.now)

    def __str__(self) -> str:
        """Return a formatted string representation of the payload."""
        return f"ErrorPayload(error={self.error.error_type.value}: {self.error.message[:100]}..., should_stop={self.should_stop})"


@dataclass
class AIDecisionPayload:
    """Payload for AI decision signals."""
    analysis: AIAnalysis
    affected_urls: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class StatisticsPayload:
    """Payload for statistics update signals."""
    statistics: CrawlStatistics
    timestamp: datetime = field(default_factory=datetime.now)


# =============================================================================
# Control Commands
# =============================================================================

@dataclass
class StartCrawlCommand:
    """Command to start crawling."""
    config: CrawlerConfig
    ai_config: AIConfig
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class StopCrawlCommand:
    """Command to stop crawling."""
    session_id: str
    force: bool = False
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class PauseCrawlCommand:
    """Command to pause crawling."""
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ResumeCrawlCommand:
    """Command to resume crawling."""
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ConfigUpdateCommand:
    """Command to update configuration during crawling."""
    session_id: str
    payload: ConfigChangePayload
    timestamp: datetime = field(default_factory=datetime.now)


# =============================================================================
# Utility Functions
# =============================================================================

def create_activity_log_entry(
    activity_type: ActivityType,
    message: str,
    level: LogLevel = LogLevel.INFO,
    url: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    session_id: Optional[str] = None
) -> ActivityLogEntry:
    """Create a standardized activity log entry."""
    return ActivityLogEntry(
        activity_type=activity_type,
        message=message,
        level=level,
        url=url,
        details=details or {},
        session_id=session_id
    )


def create_error_info(
    error_type: str,
    message: str,
    url: Optional[str] = None,
    stack_trace: Optional[str] = None,
    is_recoverable: bool = True,
    session_id: Optional[str] = None
) -> ErrorInfo:
    """Create a standardized error info object."""
    return ErrorInfo(
        error_type=error_type,
        message=message,
        url=url,
        stack_trace=stack_trace,
        is_recoverable=is_recoverable,
        session_id=session_id
    )


def create_url_data(
    url: str,
    title: Optional[str] = None,
    parent_url: Optional[str] = None,
    depth: int = 0,
    priority: float = 1.0
) -> URLData:
    """Create a standardized URL data object."""
    return URLData(
        url=url,
        title=title,
        parent_url=parent_url,
        depth=depth,
        priority=priority
    )


# =============================================================================
# Priority Strategy Interface
# =============================================================================

class PriorityStrategy:
    """Protocol for priority calculation strategies."""
    def calculate_priority(self, url_data: 'URLData') -> float:
        """Calculate priority for a URLData object."""
        raise NotImplementedError


# =============================================================================
# Type Aliases for Signal Payloads
# =============================================================================

SignalPayload = Union[
    ConfigChangePayload,
    StatusUpdatePayload,
    PageProcessedPayload,
    ActivityLogPayload,
    ErrorPayload,
    AIDecisionPayload
]

CommandPayload = Union[
    StartCrawlCommand,
    StopCrawlCommand,
    PauseCrawlCommand,
    ResumeCrawlCommand,
    ConfigUpdateCommand
]
