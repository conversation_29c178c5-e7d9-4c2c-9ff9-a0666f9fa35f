"""
Centralized Signal-Slot Architecture for Crawler9000

This module provides a centralized SignalBus that manages all inter-component
communication using Qt's signal-slot mechanism with typed payloads from models.py.
This decouples GUI modules from each other and from the backend components.

Key Features:
- Type-safe signal emissions with pydantic-like validation
- Centralized signal routing and debugging
- Automatic signal logging and monitoring
- Weak reference management to prevent memory leaks
- Thread-safe signal emission across GUI and backend threads
- Signal buffering and replay capabilities for debugging

Architecture:
- SignalBus acts as a singleton mediator between all components
- All signals use shared models.py payloads for type safety
- Components register with the bus and communicate exclusively through it
- Supports both Qt signals (for GUI) and custom events (for backend)
"""

import logging
import weakref
from typing import Dict, List, Set, Optional, Callable, Any, Union, Type
from dataclasses import dataclass, field
from datetime import datetime
from threading import Lock, RLock
from enum import Enum, auto
import uuid

from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread, QMutex, QMutexLocker
from PyQt6.QtWidgets import QApplication

from .models import (
    # Import all signal payload types
    SignalPayload, CommandPayload,
    ConfigChangePayload, StatusUpdatePayload, PageProcessedPayload,
    ActivityLogPayload, ErrorPayload, AIDecisionPayload,
    StartCrawlCommand, StopCrawlCommand, PauseCrawlCommand,
    ResumeCrawlCommand, ConfigUpdateCommand,
    CrawlStatus, LogLevel, ActivityType, AIDecisionType
)

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """Enumeration of available signal types."""
    # Status and lifecycle signals
    STATUS_UPDATE = "status_update"
    CONFIG_CHANGED = "config_changed"
    
    # Crawling process signals
    CRAWL_STARTED = "crawl_started"
    CRAWL_STOPPED = "crawl_stopped"
    CRAWL_PAUSED = "crawl_paused"
    CRAWL_RESUMED = "crawl_resumed"
    PAGE_PROCESSED = "page_processed"
    STATISTICS_UPDATE = "statistics_update"
    
    # Activity and logging signals
    ACTIVITY_LOG = "activity_log"
    ERROR_OCCURRED = "error_occurred"
    
    # AI decision signals
    AI_DECISION = "ai_decision"
    
    # Command signals (for backend communication)
    START_CRAWL_COMMAND = "start_crawl_command"
    STOP_CRAWL_COMMAND = "stop_crawl_command"
    PAUSE_CRAWL_COMMAND = "pause_crawl_command"
    RESUME_CRAWL_COMMAND = "resume_crawl_command"
    CONFIG_UPDATE_COMMAND = "config_update_command"


@dataclass
class SignalRegistration:
    """Registration entry for signal handlers."""
    component_id: str
    signal_type: SignalType
    handler: Callable[[Any], None]
    component_ref: weakref.ReferenceType
    thread_safe: bool = True
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SignalEvent:
    """Represents a signal event for logging and debugging."""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    signal_type: SignalType = None
    payload: SignalPayload = None
    sender_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    processing_time: Optional[float] = None
    recipient_count: int = 0
    error: Optional[str] = None


class SignalBusError(Exception):
    """Base exception for SignalBus errors."""
    pass


class ThreadSafeSignalEmitter(QObject):
    """Thread-safe signal emitter for Qt signals."""
    
    # Define all Qt signals with proper typing
    statusUpdated = pyqtSignal(object)  # StatusUpdatePayload
    configChanged = pyqtSignal(object)  # ConfigChangePayload
    crawlStarted = pyqtSignal(object)   # StartCrawlCommand
    crawlStopped = pyqtSignal(object)   # StopCrawlCommand
    crawlPaused = pyqtSignal(object)    # PauseCrawlCommand
    crawlResumed = pyqtSignal(object)   # ResumeCrawlCommand
    pageProcessed = pyqtSignal(object)  # PageProcessedPayload
    activityLogged = pyqtSignal(object) # ActivityLogPayload
    errorOccurred = pyqtSignal(object)  # ErrorPayload
    aiDecisionMade = pyqtSignal(object) # AIDecisionPayload
    
    # Command signals
    startCrawlCommand = pyqtSignal(object)  # StartCrawlCommand
    stopCrawlCommand = pyqtSignal(object)   # StopCrawlCommand
    pauseCrawlCommand = pyqtSignal(object)  # PauseCrawlCommand
    resumeCrawlCommand = pyqtSignal(object) # ResumeCrawlCommand
    configUpdateCommand = pyqtSignal(object) # ConfigUpdateCommand

    def __init__(self):
        super().__init__()
        # Map signal types to actual Qt signals
        self._signal_map = {
            SignalType.STATUS_UPDATE: self.statusUpdated,
            SignalType.CONFIG_CHANGED: self.configChanged,
            SignalType.CRAWL_STARTED: self.crawlStarted,
            SignalType.CRAWL_STOPPED: self.crawlStopped,
            SignalType.CRAWL_PAUSED: self.crawlPaused,
            SignalType.CRAWL_RESUMED: self.crawlResumed,
            SignalType.PAGE_PROCESSED: self.pageProcessed,
            SignalType.ACTIVITY_LOG: self.activityLogged,
            SignalType.ERROR_OCCURRED: self.errorOccurred,
            SignalType.AI_DECISION: self.aiDecisionMade,
            SignalType.START_CRAWL_COMMAND: self.startCrawlCommand,
            SignalType.STOP_CRAWL_COMMAND: self.stopCrawlCommand,
            SignalType.PAUSE_CRAWL_COMMAND: self.pauseCrawlCommand,
            SignalType.RESUME_CRAWL_COMMAND: self.resumeCrawlCommand,
            SignalType.CONFIG_UPDATE_COMMAND: self.configUpdateCommand,
        }

    def emit_signal(self, signal_type: SignalType, payload: Any) -> None:
        """Emit a signal with the given payload."""
        qt_signal = self._signal_map.get(signal_type)
        if qt_signal:
            qt_signal.emit(payload)
        else:
            logger.warning(f"Unknown signal type: {signal_type}")


class SignalBus(QObject):
    """
    Centralized SignalBus for managing all inter-component communication.
    
    This class implements the Mediator pattern and provides:
    - Type-safe signal emission and handling
    - Component registration and lifecycle management
    - Signal logging and debugging capabilities
    - Thread-safe operation across GUI and backend threads
    - Automatic cleanup of weak references
    """

    def __init__(self):
        # Initialize QObject first
        super().__init__()
        
        # Thread safety
        self._lock = RLock()
        self._qt_mutex = QMutex()
        
        # Component and signal management
        self._registrations: Dict[SignalType, List[SignalRegistration]] = {}
        self._components: Dict[str, weakref.ReferenceType] = {}
        self._signal_history: List[SignalEvent] = []
        self._max_history = 1000  # Configurable history size
        
        # Qt signal emitter for thread-safe GUI communication
        self._emitter = ThreadSafeSignalEmitter()
        
        # Debug and monitoring
        self._debug_mode = False
        self._signal_stats: Dict[SignalType, int] = {}
        
        # Cleanup timer for weak references
        self._cleanup_timer = QTimer()
        self._cleanup_timer.timeout.connect(self._cleanup_dead_references)
        self._cleanup_timer.start(30000)  # Cleanup every 30 seconds
        
        logger.info("SignalBus initialized as singleton")

    @classmethod
    def instance(cls) -> 'SignalBus':
        """Get the singleton instance of SignalBus."""
        return cls()

    def set_debug_mode(self, enabled: bool) -> None:
        """Enable or disable debug mode for detailed signal logging."""
        with self._lock:
            self._debug_mode = enabled
            logger.info(f"SignalBus debug mode {'enabled' if enabled else 'disabled'}")

    def register_component(self, component_id: str, component: QObject) -> None:
        """
        Register a component with the SignalBus.
        
        Args:
            component_id: Unique identifier for the component
            component: The component object (will be stored as weak reference)
        """
        with self._lock:
            # Store weak reference to prevent memory leaks
            self._components[component_id] = weakref.ref(
                component, 
                lambda ref: self._cleanup_component(component_id)
            )
            
            if self._debug_mode:
                logger.debug(f"Component registered: {component_id}")

    def unregister_component(self, component_id: str) -> None:
        """
        Unregister a component and all its signal handlers.
        
        Args:
            component_id: The component ID to unregister
        """
        with self._lock:
            # Remove from components
            if component_id in self._components:
                del self._components[component_id]
            
            # Remove all registrations for this component
            for signal_type in list(self._registrations.keys()):
                self._registrations[signal_type] = [
                    reg for reg in self._registrations[signal_type]
                    if reg.component_id != component_id
                ]
                
                # Remove empty lists
                if not self._registrations[signal_type]:
                    del self._registrations[signal_type]
            
            if self._debug_mode:
                logger.debug(f"Component unregistered: {component_id}")

    def connect(
        self, 
        signal_type: SignalType, 
        handler: Callable[[Any], None],
        component_id: str,
        thread_safe: bool = True
    ) -> None:
        """
        Connect a handler to a specific signal type.
        
        Args:
            signal_type: The type of signal to listen for
            handler: The callback function to handle the signal
            component_id: ID of the component registering the handler
            thread_safe: Whether the handler can be called from any thread
        """
        with self._lock:
            # Ensure component is registered
            if component_id not in self._components:
                raise SignalBusError(f"Component {component_id} not registered")
            
            component_ref = self._components[component_id]
            
            # Create registration
            registration = SignalRegistration(
                component_id=component_id,
                signal_type=signal_type,
                handler=handler,
                component_ref=component_ref,
                thread_safe=thread_safe
            )
            
            # Add to registrations
            if signal_type not in self._registrations:
                self._registrations[signal_type] = []
            
            self._registrations[signal_type].append(registration)
            
            # Connect Qt signal if in GUI thread
            if QApplication.instance() and hasattr(self._emitter, '_signal_map'):
                qt_signal = self._emitter._signal_map.get(signal_type)
                if qt_signal:
                    qt_signal.connect(handler)
            
            if self._debug_mode:
                logger.debug(f"Handler connected: {component_id} -> {signal_type}")

    def disconnect(self, signal_type: SignalType, component_id: str) -> None:
        """
        Disconnect all handlers for a signal type from a specific component.
        
        Args:
            signal_type: The signal type to disconnect from
            component_id: The component ID
        """
        with self._lock:
            if signal_type in self._registrations:
                original_count = len(self._registrations[signal_type])
                
                # Remove registrations for this component
                self._registrations[signal_type] = [
                    reg for reg in self._registrations[signal_type]
                    if reg.component_id != component_id
                ]
                
                # Clean up empty lists
                if not self._registrations[signal_type]:
                    del self._registrations[signal_type]
                
                removed_count = original_count - len(self._registrations.get(signal_type, []))
                
                if self._debug_mode and removed_count > 0:
                    logger.debug(f"Disconnected {removed_count} handlers: {component_id} -> {signal_type}")

    def emit(
        self, 
        signal_type: SignalType, 
        payload: SignalPayload,
        sender_id: Optional[str] = None
    ) -> None:
        """
        Emit a signal with the specified payload.
        
        Args:
            signal_type: The type of signal to emit
            payload: The signal payload (must be from models.py)
            sender_id: Optional ID of the sending component
        """
        start_time = datetime.now()
        event = SignalEvent(
            signal_type=signal_type,
            payload=payload,
            sender_id=sender_id,
            timestamp=start_time
        )
        
        try:
            with self._lock:
                # Validate payload type
                self._validate_payload(signal_type, payload)
                
                # Update statistics
                self._signal_stats[signal_type] = self._signal_stats.get(signal_type, 0) + 1
                
                # Get handlers for this signal type
                handlers = self._registrations.get(signal_type, [])
                event.recipient_count = len(handlers)
                
                # Emit using Qt signals for thread safety
                self._emitter.emit_signal(signal_type, payload)
                
                # Call direct handlers (for non-Qt components)
                self._call_direct_handlers(handlers, payload)
                
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                event.processing_time = processing_time
                
                # Add to history
                self._add_to_history(event)
                
                if self._debug_mode:
                    logger.debug(
                        f"Signal emitted: {signal_type} "
                        f"(sender: {sender_id}, recipients: {event.recipient_count}, "
                        f"time: {processing_time:.3f}s)"
                    )
                    
        except Exception as e:
            error_msg = f"Error emitting signal {signal_type}: {str(e)}"
            event.error = error_msg
            logger.error(error_msg, exc_info=True)
            self._add_to_history(event)
            raise SignalBusError(error_msg) from e

    def emit_command(
        self,
        command: CommandPayload,
        sender_id: Optional[str] = None
    ) -> None:
        """
        Emit a command signal (convenience method).
        
        Args:
            command: The command payload
            sender_id: Optional ID of the sending component
        """
        # Map command types to signal types
        command_map = {
            StartCrawlCommand: SignalType.START_CRAWL_COMMAND,
            StopCrawlCommand: SignalType.STOP_CRAWL_COMMAND,
            PauseCrawlCommand: SignalType.PAUSE_CRAWL_COMMAND,
            ResumeCrawlCommand: SignalType.RESUME_CRAWL_COMMAND,
            ConfigUpdateCommand: SignalType.CONFIG_UPDATE_COMMAND,
        }
        
        signal_type = command_map.get(type(command))
        if signal_type:
            self.emit(signal_type, command, sender_id)
        else:
            raise SignalBusError(f"Unknown command type: {type(command)}")

    def _validate_payload(self, signal_type: SignalType, payload: Any) -> None:
        """Validate that the payload matches the expected type for the signal."""
        # Define expected payload types for each signal
        expected_types = {
            SignalType.STATUS_UPDATE: StatusUpdatePayload,
            SignalType.CONFIG_CHANGED: ConfigChangePayload,
            SignalType.PAGE_PROCESSED: PageProcessedPayload,
            SignalType.ACTIVITY_LOG: ActivityLogPayload,
            SignalType.ERROR_OCCURRED: ErrorPayload,
            SignalType.AI_DECISION: AIDecisionPayload,
            SignalType.START_CRAWL_COMMAND: StartCrawlCommand,
            SignalType.STOP_CRAWL_COMMAND: StopCrawlCommand,
            SignalType.PAUSE_CRAWL_COMMAND: PauseCrawlCommand,
            SignalType.RESUME_CRAWL_COMMAND: ResumeCrawlCommand,
            SignalType.CONFIG_UPDATE_COMMAND: ConfigUpdateCommand,
        }
        
        expected_type = expected_types.get(signal_type)
        if expected_type and not isinstance(payload, expected_type):
            raise SignalBusError(
                f"Invalid payload type for {signal_type}: "
                f"expected {expected_type.__name__}, got {type(payload).__name__}"
            )

    def _call_direct_handlers(self, handlers: List[SignalRegistration], payload: Any) -> None:
        """Call direct (non-Qt) signal handlers."""
        for registration in handlers[:]:  # Copy to prevent modification during iteration
            try:
                # Check if component still exists (weak reference)
                component = registration.component_ref()
                if component is None:
                    # Component was garbage collected, will be cleaned up later
                    continue
                
                # Call the handler
                if registration.thread_safe or QThread.currentThread() == self.thread():
                    registration.handler(payload)
                else:
                    # Use Qt's queued connection for thread safety
                    QTimer.singleShot(0, lambda p=payload, h=registration.handler: h(p))
                    
            except Exception as e:
                logger.error(
                    f"Error calling handler for {registration.signal_type} "
                    f"from {registration.component_id}: {e}",
                    exc_info=True
                )

    def _cleanup_component(self, component_id: str) -> None:
        """Cleanup callback when a component is garbage collected."""
        with self._lock:
            if component_id in self._components:
                del self._components[component_id]
            
            if self._debug_mode:
                logger.debug(f"Component garbage collected: {component_id}")

    def _cleanup_dead_references(self) -> None:
        """Cleanup dead weak references periodically."""
        try:
            with self._lock:
                # Clean up dead component references
                dead_components = []
                for comp_id, weak_ref in self._components.items():
                    if weak_ref() is None:
                        dead_components.append(comp_id)
                
                for comp_id in dead_components:
                    self.unregister_component(comp_id)
                
                # Clean up dead handler references
                for signal_type in list(self._registrations.keys()):
                    alive_registrations = []
                    for reg in self._registrations[signal_type]:
                        if reg.component_ref() is not None:
                            alive_registrations.append(reg)
                    
                    if alive_registrations:
                        self._registrations[signal_type] = alive_registrations
                    else:
                        del self._registrations[signal_type]
                
                if self._debug_mode and dead_components:
                    logger.debug(f"Cleaned up {len(dead_components)} dead component references")
                    
        except Exception as e:
            logger.error(f"Error during reference cleanup: {e}", exc_info=True)

    def _add_to_history(self, event: SignalEvent) -> None:
        """Add an event to the signal history."""
        self._signal_history.append(event)
        
        # Trim history if it gets too long
        if len(self._signal_history) > self._max_history:
            self._signal_history = self._signal_history[-self._max_history:]

    def get_signal_stats(self) -> Dict[str, Any]:
        """Get signal bus statistics."""
        with self._lock:
            return {
                'total_signals_emitted': sum(self._signal_stats.values()),
                'signals_by_type': dict(self._signal_stats),
                'active_components': len(self._components),
                'active_registrations': sum(len(regs) for regs in self._registrations.values()),
                'history_size': len(self._signal_history),
                'debug_mode': self._debug_mode
            }

    def get_signal_history(self, limit: Optional[int] = None) -> List[SignalEvent]:
        """Get recent signal history."""
        with self._lock:
            history = self._signal_history.copy()
            if limit:
                history = history[-limit:]
            return history

    def clear_history(self) -> None:
        """Clear signal history."""
        with self._lock:
            self._signal_history.clear()
            logger.info("Signal history cleared")

    def reset(self) -> None:
        """Reset the SignalBus to initial state (mainly for testing)."""
        with self._lock:
            self._registrations.clear()
            self._components.clear()
            self._signal_history.clear()
            self._signal_stats.clear()
            logger.info("SignalBus reset to initial state")


# Global SignalBus instance
_global_signal_bus: Optional[SignalBus] = None

# Convenience functions for easy access
def get_signal_bus() -> SignalBus:
    """Get the global SignalBus instance."""
    global _global_signal_bus
    if _global_signal_bus is None:
        _global_signal_bus = SignalBus()
    return _global_signal_bus


def emit_signal(signal_type: SignalType, payload: SignalPayload, sender_id: Optional[str] = None) -> None:
    """Convenience function to emit a signal."""
    get_signal_bus().emit(signal_type, payload, sender_id)


def emit_command(command: CommandPayload, sender_id: Optional[str] = None) -> None:
    """Convenience function to emit a command."""
    get_signal_bus().emit_command(command, sender_id)


def connect_signal(
    signal_type: SignalType, 
    handler: Callable[[Any], None],
    component_id: str,
    thread_safe: bool = True
) -> None:
    """Convenience function to connect to a signal."""
    get_signal_bus().connect(signal_type, handler, component_id, thread_safe)


def register_component(component_id: str, component: QObject) -> None:
    """Convenience function to register a component."""
    get_signal_bus().register_component(component_id, component)
