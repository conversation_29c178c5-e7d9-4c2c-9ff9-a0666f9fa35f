"""
URL management for Crawler9000

This module handles URL validation, filtering, and management
including robots.txt compliance and domain restrictions.
Now with thread-safe PriorityQueue support for asynchronous operations.
"""

import urllib.robotparser
from urllib.parse import urlparse, urljoin, urldefrag
from typing import List, Set, Optional, Tuple, Dict, Any
import logging
import re
import asyncio
import queue
import time
import threading
from collections import defaultdict
from .priority_strategy import DefaultPriorityStrategy

logger = logging.getLogger(__name__)


class URLManager:
    """
    Thread-safe URL manager for crawling with priority queue support.
    Uses (-priority, depth, timestamp) tuples for proper prioritization.
    """

    def __init__(self, config):
        self.priority_strategy = DefaultPriorityStrategy()  # Default Strategy
        self.config = config
        self.visited_urls: Set[str] = set()
        
        # Thread-safe priority queue: (-priority, depth, timestamp, url)
        # Negative priority ensures higher priority values come first
        self.to_visit_queue = queue.PriorityQueue()
        
        # Thread-safe sets and counters
        self._visited_lock = threading.RLock()
        self._queue_lock = threading.RLock()
        
        # URL metadata tracking
        self.url_metadata = {}  # url -> {priority, depth, parent_url, etc.}
        
        # Robots.txt cache with thread safety
        self.robots_cache: dict = {}
        self._robots_lock = threading.RLock()
        
        # Statistics
        self.stats = {
            'urls_added': 0,
            'urls_processed': 0,
            'urls_filtered': 0
        }
        
        logger.info("URLManager initialized with thread-safe PriorityQueue")

    def promote(self, url: str, increase_by: float = 0.1):
        """
        Promote the priority of a given URL by a certain amount.
        """
        with self._queue_lock:
            if url in self.url_metadata:
                entry = self.url_metadata[url]
                entry['priority'] += increase_by
                entry['priority'] = min(entry['priority'], 1.0)  # Cap priority
                self._reinsert_with_updated_priority(url)

    def demote(self, url: str, decrease_by: float = 0.1):
        """
        Demote the priority of a given URL by a certain amount.
        """
        with self._queue_lock:
            if url in self.url_metadata:
                entry = self.url_metadata[url]
                entry['priority'] -= decrease_by
                entry['priority'] = max(entry['priority'], 0.0)  # Lower bound
                self._reinsert_with_updated_priority(url)

    def _reinsert_with_updated_priority(self, url: str):
        """
        Reinsert a URL into the priority queue with updated priority.
        """
        entry = self.url_metadata[url]
        timestamp = entry['added_at']
        depth = entry['depth']
        priority_tuple = (-entry['priority'], depth, timestamp, url)
        self.to_visit_queue.put(priority_tuple)

    def add_urls(self, urls: List[str], base_url: str = None, priority: float = 1.0, depth: int = 0):
        """
        Add URLs to the crawling queue after validation and filtering.
        
        Args:
            urls: List of URLs to add
            base_url: Base URL for resolving relative URLs
            priority: Priority for URL processing (higher = more important)
            depth: Crawl depth for these URLs
        """
        with self._queue_lock:
            for url in urls:
                clean_url = self.clean_url(url, base_url)
                if clean_url and self.is_valid_url(clean_url):
                    # Check if already visited or queued
                    with self._visited_lock:
                        if clean_url in self.visited_urls:
                            continue
                    
                    # Check if already in queue metadata (avoid duplicates)
                    if clean_url in self.url_metadata:
                        continue
                    
                    # Create priority tuple: (-priority, depth, timestamp, url)
                    timestamp = time.time()
                    priority_tuple = (-priority, depth, timestamp, clean_url)
                    
                    # Add to queue
                    self.to_visit_queue.put(priority_tuple)
                    
                    # Store metadata
                    self.url_metadata[clean_url] = {
                        'priority': priority,
                        'depth': depth,
                        'parent_url': base_url,
                        'added_at': timestamp
                    }
                    
                    self.stats['urls_added'] += 1
                    logger.debug(f"Added URL to priority queue: {clean_url} (priority={priority}, depth={depth})")

    def get_next_url(self) -> Optional[Tuple[str, dict]]:
        """
        Get the next URL to crawl from the priority queue.
        
        Returns:
            Tuple of (url, metadata) or None if queue is empty
        """
        try:
            # Get the highest priority URL from queue (non-blocking)
            priority_tuple = self.to_visit_queue.get_nowait()
            neg_priority, depth, timestamp, url = priority_tuple
            
            # Mark as visited
            with self._visited_lock:
                self.visited_urls.add(url)
            
            # Get metadata
            metadata = self.url_metadata.get(url, {})
            metadata.update({
                'actual_priority': -neg_priority,
                'depth': depth,
                'queued_at': timestamp
            })
            
            self.stats['urls_processed'] += 1
            logger.debug(f"Retrieved URL from priority queue: {url} (priority={-neg_priority}, depth={depth})")
            
            return url, metadata
            
        except queue.Empty:
            return None

    def clean_url(self, url: str, base_url: str = None) -> Optional[str]:
        """
        Clean and normalize a URL.
        """
        try:
            # Convert relative URLs to absolute
            if base_url:
                url = urljoin(base_url, url)
            
            # Remove fragment identifiers
            url, _ = urldefrag(url)
            
            # Basic URL validation
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return None
                
            # Reconstruct clean URL
            clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            if parsed.query:
                clean_url += f"?{parsed.query}"
                
            return clean_url
            
        except Exception as e:
            logger.error(f"Error cleaning URL {url}: {e}")
            return None

    def is_valid_url(self, url: str) -> bool:
        """
        Check if a URL is valid for crawling based on configuration rules.
        """
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Check allowed domains
        if self.config.allowed_domains:
            if not any(allowed in domain for allowed in self.config.allowed_domains):
                logger.debug(f"URL rejected - not in allowed domains: {url}")
                return False

        # Check blocked domains
        if self.config.blocked_domains:
            if any(blocked in domain for blocked in self.config.blocked_domains):
                logger.debug(f"URL rejected - in blocked domains: {url}")
                return False

        # Check external links policy
        if not self.config.follow_external_links:
            # This would need a base domain to compare against
            # For now, we'll implement this logic in the crawler
            pass

        # If we get here, the URL is valid
        return True

    def exploration_exploitation_choice(self, epsilon: float = 0.1) -> Optional[str]:
        """Choose between exploration and exploitation using -greedy strategy."""
        import random
        if random.random() < epsilon:
            # Exploration: choose a random URL
            return random.choice(list(self.url_metadata.keys())) if self.url_metadata else None
        else:
            # Exploitation: choose the best-ranked URL
            return self.get_next_url()[0] if not self.to_visit_queue.empty() else None

    def select_next_url_with_ucb(self, count_visits: Dict[str, int]) -> Optional[str]:
        """Select next URL using Upper Confidence Bound (UCB) strategy."""
        total_visits = sum(count_visits.values())
        best_value = -float('inf')
        best_url = None

        for url, times_visited in count_visits.items():
            score = self.url_metadata[url]['priority'] + \
                    (2 * math.sqrt(math.log(total_visits + 1) / (times_visited + 1)))
            if score > best_value:
                best_value = score
                best_url = url

        return best_url

        # Check robots.txt if enabled (make it optional to avoid hanging)
        if self.config.respect_robots_txt:
            try:
                if not self.is_allowed_by_robots(url):
                    logger.debug(f"URL rejected - blocked by robots.txt: {url}")
                    return False
            except Exception as e:
                logger.warning(f"Could not check robots.txt for {url}: {e}")
                # Continue anyway if robots.txt check fails

        # Check file type
        if self.config.allowed_file_types:
            path = parsed.path.lower()
            if path.endswith(tuple(f".{ext}" for ext in self.config.allowed_file_types)):
                return True
            # If it doesn't end with a file extension, assume it's a page
            if not re.search(r'\.[a-zA-Z0-9]+$', path):
                return True
            logger.debug(f"URL rejected - file type not allowed: {url}")
            return False

        return True

    def is_allowed_by_robots(self, url: str) -> bool:
        """
        Check if a URL is allowed by robots.txt.
        """
        parsed = urlparse(url)
        base_url = f"{parsed.scheme}://{parsed.netloc}"
        
        if base_url not in self.robots_cache:
            try:
                robots_url = urljoin(base_url, '/robots.txt')
                rp = urllib.robotparser.RobotFileParser()
                rp.set_url(robots_url)
                rp.read()
                self.robots_cache[base_url] = rp
                logger.debug(f"Loaded robots.txt for {base_url}")
            except Exception as e:
                logger.warning(f"Failed to load robots.txt for {base_url}: {e}")
                # If we can't load robots.txt, assume allowed
                self.robots_cache[base_url] = None

        robots_parser = self.robots_cache[base_url]
        if robots_parser:
            return robots_parser.can_fetch('Crawler9000', url)
        
        return True

    def get_queue_status(self) -> dict:
        """
        Get the current status of the URL queue.
        """
        with self._visited_lock:
            visited_count = len(self.visited_urls)
        
        # Get queue size (thread-safe)
        queue_size = self.to_visit_queue.qsize()
        
        return {
            'visited_count': visited_count,
            'queue_count': queue_size,
            'total_discovered': visited_count + queue_size,
            'urls_added': self.stats['urls_added'],
            'urls_processed': self.stats['urls_processed'],
            'urls_filtered': self.stats['urls_filtered']
        }

    def has_urls_to_process(self) -> bool:
        """
        Check if there are URLs left to process.
        """
        return not self.to_visit_queue.empty()

    def clear_queue(self):
        """
        Clear the URL queue (useful for stopping/resetting crawl).
        """
        with self._queue_lock:
            # Clear the priority queue
            while not self.to_visit_queue.empty():
                try:
                    self.to_visit_queue.get_nowait()
                except queue.Empty:
                    break
            
            # Clear metadata
            self.url_metadata.clear()
            
        logger.info("URL priority queue cleared")

    def get_visited_urls(self) -> Set[str]:
        """
        Get the set of visited URLs.
        """
        with self._visited_lock:
            return self.visited_urls.copy()

    def get_queue_urls(self) -> List[str]:
        """
        Get the list of URLs currently in the queue.
        Note: This is expensive for PriorityQueue and should be used sparingly.
        """
        urls = []
        temp_items = []
        
        with self._queue_lock:
            # Temporarily drain queue to get URLs
            while not self.to_visit_queue.empty():
                try:
                    item = self.to_visit_queue.get_nowait()
                    temp_items.append(item)
                    urls.append(item[3])  # URL is at index 3
                except queue.Empty:
                    break
            
            # Put items back
            for item in temp_items:
                self.to_visit_queue.put(item)
        
        return urls
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get detailed statistics about URL management.
        """
        return {
            **self.stats,
            'queue_size': self.to_visit_queue.qsize(),
            'visited_count': len(self.visited_urls),
            'metadata_entries': len(self.url_metadata),
            'robots_cache_size': len(self.robots_cache)
        }
