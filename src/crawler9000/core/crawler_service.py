import logging
from PyQt6.QtCore import QObject, QThread, pyqtSignal, pyqtSlot
from .crawler_engine import CrawlerEngine

logger = logging.getLogger(__name__)
from ..shared.models import (
    StatusUpdatePayload,
    PageProcessedPayload,
    ActivityLogPayload,
    ErrorPayload,
    StartCrawlCommand,
    StopCrawlCommand,
    PauseCrawlCommand,
    ResumeCrawlCommand,
    ConfigUpdateCommand
)
from ..utils.config import get_config

class CrawlerService(QObject):
    status_update = pyqtSignal(StatusUpdatePayload)
    page_processed = pyqtSignal(PageProcessedPayload)
    activity_log = pyqtSignal(ActivityLogPayload)
    error_occurred = pyqtSignal(ErrorPayload)

    def __init__(self):
        super().__init__()
        self.config = get_config()
        self.engine_thread = QThread()
        self.engine = CrawlerEngine(self.config)
        self.engine.moveToThread(self.engine_thread)
        self._is_starting = False  # Flag to prevent multiple simultaneous starts

        # Connect signals and slots
        self.engine_thread.started.connect(self.run_crawler)
        self.engine_thread.finished.connect(self.cleanup)
        self.engine.status_update.connect(self.handle_status_update)
        self.engine.page_processed.connect(self.handle_page_processed)
        self.engine.activity_log.connect(self.handle_activity_log)
        self.engine.error_occurred.connect(self.handle_error)

    @pyqtSlot(StartCrawlCommand)
    def start(self, command: StartCrawlCommand):
        # Prevent multiple simultaneous starts
        if self._is_starting or self.engine_thread.isRunning():
            logger.warning("Crawler is already starting or running, ignoring start command")
            return

        self._is_starting = True
        try:
            self.config.crawler = command.config
            self.config.ai = command.ai_config
            self.engine_thread.start()
        finally:
            self._is_starting = False
    
    def run_crawler(self):
        start_urls = [self.config.crawler.start_url]
        # Run the async crawler in an event loop
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.engine.start_crawling(start_urls))
        except Exception as e:
            logger.error(f"Error running crawler: {e}")
        finally:
            loop.close()

    @pyqtSlot(StopCrawlCommand)
    def stop(self, command: StopCrawlCommand):
        if self.engine_thread.isRunning():
            # Use the synchronous force stop method instead of async stop_crawling
            self.engine.force_stop_crawling()
            self.engine_thread.quit()
            self.engine_thread.wait()

    @pyqtSlot(PauseCrawlCommand)
    def pause(self, command: PauseCrawlCommand):
        self.engine.is_running = False

    @pyqtSlot(ResumeCrawlCommand)
    def resume(self, command: ResumeCrawlCommand):
        self.engine.is_running = True
        if not self.engine_thread.isRunning():
            self.engine_thread.start()

    @pyqtSlot(ConfigUpdateCommand)
    def update_config(self, command: ConfigUpdateCommand):
        self.config = command.payload.crawler_config or self.config
        self.engine.update_config(self.config.crawler)

    def cleanup(self):
        self.engine.cleanup()

    def handle_status_update(self, payload):
        self.status_update.emit(payload)

    def handle_page_processed(self, payload):
        self.page_processed.emit(payload)

    def handle_activity_log(self, payload):
        self.activity_log.emit(payload)

    def handle_error(self, payload):
        self.error_occurred.emit(payload)
        if payload.should_stop:
            self.stop()
