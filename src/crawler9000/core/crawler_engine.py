"""
Web crawler engine for Crawler9000

This module handles the core crawling logic, including:
- URL management
- Page downloading
- Content parsing
- AI integration for analysis
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Set, Any
from urllib.parse import urlparse

from PyQt6.QtCore import QObject, pyqtSignal

from .url_manager import URLManager
from .content_parser import ContentParser
from .ai_integration import AIAnalyzer
from .async_url_fetcher import AsyncURLFetcher

logger = logging.getLogger(__name__)


class CrawlerEngine(QObject):
    """
    Core web crawling engine that manages crawling operations asynchronously.
    """

    # PyQt signals for communication with the service
    status_update = pyqtSignal(object)  # StatusUpdatePayload
    page_processed = pyqtSignal(object)  # PageProcessedPayload
    activity_log = pyqtSignal(object)  # ActivityLogPayload
    error_occurred = pyqtSignal(object)  # ErrorPayload

    def __init__(self, config):
        super().__init__()
        self.config = config
        self.url_manager = URLManager(config.crawler)
        self.content_parser = ContentParser(config.crawler)
        self.ai_analyzer = AIAnalyzer(config)
        
        # Crawling state
        self.is_running = False
        self.crawled_pages = []
        self.start_domain = None
        
        self._cancel_event = asyncio.Event()  # For graceful cancellation
        self._lock = asyncio.Lock()  # For thread-safe operations
        
        logger.info("CrawlerEngine initialized with all components")

    async def start_crawling(self, start_urls: List[str], crawl_purpose: str = "General crawling"):
        """
        Start the crawling process from a list of initial URLs asynchronously.
        
        Args:
            start_urls: List of URLs to start crawling from
            crawl_purpose: Description of the crawling purpose for AI context
        """
        logger.info(f"Starting crawl with {len(start_urls)} URLs: {start_urls}")

        self.is_running = True
        self.crawl_purpose = crawl_purpose

        # Set start domain for external link filtering
        if start_urls:
            self.start_domain = urlparse(start_urls[0]).netloc.lower()

        # Add initial URLs to queue
        self.url_manager.add_urls(start_urls)

        # Emit initial status update
        from ..shared.models import StatusUpdatePayload, CrawlStatistics, CrawlStatus
        initial_stats = CrawlStatistics(
            session_id="crawler_session",
            start_time=datetime.now(),
            status=CrawlStatus.CRAWLING,
            urls_discovered=len(start_urls),
            urls_processed=0,
            pages_crawled=0
        )
        status_payload = StatusUpdatePayload(status=CrawlStatus.CRAWLING, statistics=initial_stats)
        self.status_update.emit(status_payload)

        # Initialize AsyncURLFetcher
        async with AsyncURLFetcher(self.config) as fetcher:
            # Main crawling loop
            while (self.is_running and
                   self.url_manager.has_urls_to_process() and
                   len(self.url_manager.visited_urls) < self.config.crawler.max_pages):
                    
                next_item = self.url_manager.get_next_url()
                if next_item:
                    url, metadata = next_item  # Destructure tuple

                    try:
                        async with self._lock:  # Ensure thread safety
                            await self.process_url(fetcher, url, metadata)

                            # Emit periodic status updates
                            current_stats = self.get_crawl_statistics()
                            from ..shared.models import StatusUpdatePayload, CrawlStatistics, CrawlStatus
                            stats = CrawlStatistics(
                                session_id="crawler_session",
                                start_time=datetime.now(),
                                status=CrawlStatus.CRAWLING,
                                urls_discovered=current_stats['total_discovered'],
                                urls_processed=current_stats['visited_urls'],
                                pages_crawled=current_stats['pages_crawled']
                            )
                            status_payload = StatusUpdatePayload(status=CrawlStatus.CRAWLING, statistics=stats)
                            self.status_update.emit(status_payload)

                        # Wait for a delay; the event allows early breaking on cancel
                        await asyncio.wait_for(self._cancel_event.wait(), self.config.crawler.delay_between_requests)

                    except asyncio.TimeoutError:
                        # Normal operation (no cancel occurred), so reset event
                        self._cancel_event.clear()
                    except Exception as e:
                        logger.error(f"Error processing {url}: {e}")

                        # Emit error signal
                        from ..shared.models import ErrorPayload, ErrorInfo
                        error_info = ErrorInfo(
                            error_type='CRAWL_ERROR',
                            message=f"Error processing {url}: {str(e)}",
                            stack_trace="",
                            url=url,
                            is_recoverable=True
                        )
                        error_payload = ErrorPayload(error=error_info, should_stop=False)
                        self.error_occurred.emit(error_payload)
                        continue
        
        # Log final statistics and emit completion status
        stats = self.get_crawl_statistics()
        logger.info(f"Crawl completed. Stats: {stats}")
        self.is_running = False

        # Emit final status update
        from ..shared.models import StatusUpdatePayload, CrawlStatistics, CrawlStatus
        final_stats = CrawlStatistics(
            session_id="crawler_session",
            start_time=datetime.now(),
            status=CrawlStatus.COMPLETED,
            urls_discovered=stats['total_discovered'],
            urls_processed=stats['visited_urls'],
            pages_crawled=stats['pages_crawled']
        )
        status_payload = StatusUpdatePayload(status=CrawlStatus.COMPLETED, statistics=final_stats)
        self.status_update.emit(status_payload)

    async def process_url(self, fetcher: AsyncURLFetcher, url: str, metadata: Dict[str, Any]):
        """
        Process a single URL: download, parse, and analyze content asynchronously.
        """
        logger.info(f"Processing URL: {url} with metadata: {metadata}")
        
        # Download page content asynchronously
        content, fetch_metadata = await fetcher.fetch(url)
        if not content:
            logger.warning(f"Failed to fetch content for {url}")
            return
        
        # Parse page content
        page_data = self.content_parser.parse_page(url, content)
        
        # Add fetch metadata to page data
        page_data['fetch_metadata'] = fetch_metadata
        page_data['crawl_metadata'] = metadata
        
        # Filter external links if configured
        if not self.config.crawler.follow_external_links:
            page_data = self._filter_external_links(page_data)
        
        # AI analysis
        if self.config.ai.enable_content_analysis:
            ai_analysis = self.ai_analyzer.analyze_content(page_data)
            page_data['ai_analysis'] = ai_analysis
            
            # Check if content is valuable enough to keep
            if not self.content_parser.is_content_valuable(page_data):
                logger.info(f"Skipping low-value content: {url}")
                return
        
        # Store the crawled page data
        self.crawled_pages.append(page_data)

        # Emit page processed signal with real data
        from ..shared.models import PageProcessedPayload, PageContent
        page_content = PageContent(
            url=url,
            title=page_data.get('title', 'No title'),
            text_content=page_data.get('text_content', ''),
            word_count=page_data.get('word_count', 0)
        )

        page_processed_payload = PageProcessedPayload(
            page_content=page_content,
            ai_analysis=page_data.get('ai_analysis'),
            processing_time=0.0  # TODO: Add actual processing time tracking
        )
        self.page_processed.emit(page_processed_payload)

        # Emit activity log signal
        from ..shared.models import ActivityLogPayload, ActivityLogEntry, ActivityType
        activity_entry = ActivityLogEntry(
            activity_type=ActivityType.PAGE_PARSED,
            message=f"Successfully crawled: {url}",
            url=url,
            details={'word_count': page_data.get('word_count', 0)}
        )
        activity_payload = ActivityLogPayload(entries=[activity_entry])
        self.activity_log.emit(activity_payload)

        # Add new URLs to queue with AI prioritization
        new_urls = [link['url'] for link in page_data['internal_links']]
        
        if self.config.ai.enable_link_analysis and new_urls:
            # Use AI to prioritize URLs
            context = {'crawl_purpose': self.crawl_purpose, 'current_page': page_data}
            prioritized_urls = self.ai_analyzer.prioritize_urls(new_urls, context)
            
            # Calculate priority and depth for new URLs
            current_depth = metadata.get('depth', 0)
            new_depth = current_depth + 1
            
            # Skip if max depth reached
            if new_depth > self.config.crawler.max_depth:
                logger.info(f"Max depth reached for {url}, not adding child URLs")
                return
            
            # Add URLs with priority based on AI analysis
            for i, prioritized_url in enumerate(prioritized_urls):
                # Higher AI ranking gets higher priority
                ai_priority = len(prioritized_urls) - i
                self.url_manager.add_urls([prioritized_url], url, priority=ai_priority, depth=new_depth)
        else:
            # Add URLs with default priority
            current_depth = metadata.get('depth', 0)
            new_depth = current_depth + 1
            
            if new_depth <= self.config.crawler.max_depth:
                self.url_manager.add_urls(new_urls, url, priority=1.0, depth=new_depth)
        
        logger.info(f"Processed {url}: {page_data['word_count']} words, {len(new_urls)} new links")

    
    def _filter_external_links(self, page_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter out external links if follow_external_links is disabled.
        """
        if self.start_domain:
            filtered_links = []
            for link in page_data['links']:
                link_domain = urlparse(link['url']).netloc.lower()
                if link_domain == self.start_domain:
                    filtered_links.append(link)
            
            page_data['links'] = filtered_links
            page_data['internal_links'] = filtered_links
            page_data['external_links'] = []
        
        return page_data
    
    async def stop_crawling(self):
        """
        Stop the crawling process gracefully.
        """
        logger.info("Stopping crawl...")
        self.is_running = False
        
        # Signal cancellation event
        self._cancel_event.set()
        
        # Clear the URL queue
        self.url_manager.clear_queue()
        
        logger.info("Crawl stopped gracefully")
    
    def force_stop_crawling(self):
        """
        Force stop the crawling process (synchronous).
        """
        logger.info("Force stopping crawl...")
        self.is_running = False
        self.url_manager.clear_queue()
    
    def get_crawl_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the current crawl.
        """
        url_stats = self.url_manager.get_queue_status()
        
        return {
            'visited_urls': url_stats['visited_count'],
            'queued_urls': url_stats['queue_count'],
            'total_discovered': url_stats['total_discovered'],
            'pages_crawled': len(self.crawled_pages),
            'is_running': self.is_running,
            'start_domain': self.start_domain
        }
    
    def get_crawled_data(self) -> List[Dict[str, Any]]:
        """
        Get all crawled page data.
        """
        return self.crawled_pages.copy()
    
    def get_visited_urls(self) -> Set[str]:
        """
        Get set of visited URLs.
        """
        return self.url_manager.get_visited_urls()


# Test main crawler functionality
if __name__ == "__main__":
    from crawler9000.utils.config import ConfigManager
    import sys
    
    config_manager = ConfigManager()
    config_manager._config = config_manager.load_config()  # Load configuration
    crawler = CrawlerEngine(config=config_manager.get_config())
    
    # Use command line argument or skip test if no URL provided
    if len(sys.argv) > 1:
        test_url = sys.argv[1]
        crawler.start_crawling([test_url])
    else:
        print("Usage: python crawler_engine.py <test_url>")
        print("Example: python crawler_engine.py https://httpbin.org/html")

