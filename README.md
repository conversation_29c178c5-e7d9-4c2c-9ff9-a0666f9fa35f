# Crawler9000

**Crawler9000** is an intelligent web crawling application that combines traditional web scraping with AI-powered analysis to provide comprehensive website exploration and content analysis capabilities.

## Features

### Core Functionality
- **Intelligent Web Crawling**: Advanced crawling engine with configurable depth, domain restrictions, and content filtering
- **AI-Powered Analysis**: Multi-provider AI integration (OpenAI, Anthropic, etc.) for content analysis and decision making
- **Real-time Monitoring**: Live activity monitoring with detailed logging and statistics
- **Flexible Configuration**: Multiple crawl modes (Documentation, Research, Targeted) with customizable parameters

### User Interface
- **Modern PyQt6 GUI**: Professional desktop application with dark/light theme support
- **Activity Monitor**: Real-time log viewing with syntax highlighting and filtering
- **Statistics Dashboard**: Live charts and metrics for crawl progress and performance
- **Configuration Panel**: Intuitive settings management with validation

### Technical Features
- **Asynchronous Architecture**: High-performance async crawling with proper cancellation support
- **Signal-Based Communication**: Type-safe inter-component communication using SignalBus pattern
- **Thread Safety**: Comprehensive thread-safe operations across GUI and backend components
- **Data Persistence**: SQLite-based storage with export capabilities (JSON, CSV)
- **Rate Limiting**: Intelligent request throttling and retry mechanisms

## Installation

### Prerequisites
- Python 3.9 or higher
- PyQt6 (for GUI)
- Virtual environment (recommended)

### Quick Start

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Crawler9000
   ```

2. **Create and activate virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python -m crawler9000
   ```

### Alternative Installation Methods

#### Using Poetry (Recommended for Development)
```bash
poetry install
poetry run python -m crawler9000
```

#### Using pip with pyproject.toml
```bash
pip install -e .
crawler9000
```

## Usage

### Basic Usage

1. **Launch the application**:
   ```bash
   python -m crawler9000
   ```

2. **Configure your crawl**:
   - Enter a starting URL (e.g., `https://example.com`)
   - Select crawl mode (Documentation, Research, or Targeted)
   - Adjust intelligence level (1-5)
   - Configure advanced settings if needed

3. **Start crawling**:
   - Click "Apply" to save configuration
   - Click "Start" to begin crawling
   - Monitor progress in the Activity Monitor

### Configuration Options

#### Crawl Modes
- **Documentation**: Optimized for documentation sites with structured navigation
- **Research**: Balanced approach for general web research and content discovery
- **Targeted**: Focused crawling for specific content types or patterns

#### Advanced Settings
- **Maximum Depth**: How deep to crawl from the starting URL (1-50)
- **Maximum Pages**: Total number of pages to crawl (1-10,000)
- **Domain Restrictions**: Limit crawling to specific domains
- **External Links**: Whether to follow links outside the starting domain
- **Robots.txt**: Whether to respect robots.txt directives

### AI Configuration

Configure AI providers in the application settings:
- **OpenAI**: Requires API key for GPT models
- **Anthropic**: Requires API key for Claude models
- **Intelligence Level**: Controls AI analysis depth and frequency

## Project Structure

```
Crawler9000/
├── src/crawler9000/           # Main application source
│   ├── core/                  # Core crawling engine
│   │   ├── crawler.py         # Main crawler implementation
│   │   ├── url_manager.py     # URL queue and priority management
│   │   └── ai_analyzer.py     # AI analysis integration
│   ├── gui/                   # PyQt6 user interface
│   │   ├── main_window.py     # Main application window
│   │   ├── configuration_panel.py  # Settings panel
│   │   ├── activity_monitor.py     # Log viewer
│   │   └── statistics_dock.py      # Statistics dashboard
│   ├── shared/                # Shared models and utilities
│   │   ├── models.py          # Data models and DTOs
│   │   ├── signal_bus.py      # Inter-component communication
│   │   └── config.py          # Configuration management
│   └── utils/                 # Utility modules
├── tests/                     # Test suite
│   ├── core/                  # Core functionality tests
│   ├── gui/                   # GUI component tests
│   └── integration/           # Integration tests
├── docs/                      # Documentation
├── scripts/                   # Utility scripts
└── data/                      # Application data and logs
```

## Development

### Running Tests
```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/core/      # Core functionality
python -m pytest tests/gui/       # GUI components
python -m pytest tests/integration/  # Integration tests
```

### Code Style
The project follows PEP 8 style guidelines. Use the following tools for development:
```bash
# Code formatting
black src/ tests/

# Linting
flake8 src/ tests/

# Type checking
mypy src/
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Configuration

### Application Configuration
Configuration files are stored in:
- **Linux/macOS**: `~/.config/crawler9000/`
- **Windows**: `%APPDATA%\crawler9000\`

### Environment Variables
- `CRAWLER9000_CONFIG_DIR`: Override default config directory
- `CRAWLER9000_LOG_LEVEL`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- `OPENAI_API_KEY`: OpenAI API key for AI analysis
- `ANTHROPIC_API_KEY`: Anthropic API key for Claude models

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and virtual environment is activated
2. **PyQt6 Issues**: Install PyQt6 system packages if pip installation fails
3. **Permission Errors**: Check file permissions for config and data directories
4. **Memory Issues**: Reduce maximum pages or depth for large crawls

### Logging
Application logs are stored in:
- **Default location**: `./logs/crawler9000.log`
- **Custom location**: Set via configuration

Enable debug logging:
```bash
export CRAWLER9000_LOG_LEVEL=DEBUG
python -m crawler9000
```

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## Support

For issues, questions, or contributions:
1. Check the [documentation](docs/)
2. Search existing [issues](../../issues)
3. Create a new issue with detailed information
4. Join our community discussions

---

**Crawler9000** - Intelligent Web Crawling Made Simple
