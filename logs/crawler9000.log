[2025-08-01 13:28:10,019] INFO [root:47] Logging setup complete.
[2025-08-01 13:28:10,019] INFO [__main__:30] Starting Crawler9000...
[2025-08-01 13:28:10,020] INFO [__main__:34] Configuration loaded
[2025-08-02 02:07:01,308] INFO [root:47] Logging setup complete.
[2025-08-02 02:07:01,309] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:07:01,309] INFO [__main__:34] Configuration loaded
[2025-08-02 02:07:18,653] INFO [root:47] Logging setup complete.
[2025-08-02 02:07:18,653] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:07:18,653] INFO [__main__:34] Configuration loaded
[2025-08-02 02:09:51,188] INFO [root:47] Logging setup complete.
[2025-08-02 02:09:51,189] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:09:51,189] INFO [__main__:34] Configuration loaded
[2025-08-02 02:10:55,676] INFO [root:47] Logging setup complete.
[2025-08-02 02:10:55,677] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:10:55,677] INFO [__main__:34] Configuration loaded
[2025-08-02 02:10:55,771] INFO [crawler9000.gui.main_window:27] MainWindow initialized
[2025-08-02 02:10:55,783] INFO [__main__:49] Crawler9000 GUI started
[2025-08-02 02:11:09,957] INFO [__main__:54] Application exited with code: 0
[2025-08-02 02:14:23,479] INFO [root:47] Logging setup complete.
[2025-08-02 02:14:23,479] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:14:23,479] INFO [__main__:34] Configuration loaded
[2025-08-02 02:14:23,597] INFO [crawler9000.gui.main_window:39] MainWindow initialized
[2025-08-02 02:14:23,604] INFO [__main__:49] Crawler9000 GUI started
[2025-08-02 02:17:19,069] INFO [__main__:54] Application exited with code: 0
[2025-08-02 02:25:06,784] INFO [root:47] Logging setup complete.
[2025-08-02 02:25:06,784] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:25:06,784] INFO [__main__:34] Configuration loaded
[2025-08-02 02:25:06,909] INFO [crawler9000.gui.main_window:49] MainWindow initialized
[2025-08-02 02:25:06,913] INFO [__main__:49] Crawler9000 GUI started
[2025-08-02 02:26:39,427] INFO [crawler9000.utils.config:216] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 02:26:39,427] INFO [crawler9000.gui.main_window:578] MainWindow closed, geometry saved
[2025-08-02 02:26:39,428] INFO [__main__:54] Application exited with code: 0
[2025-08-02 02:31:17,374] INFO [root:47] Logging setup complete.
[2025-08-02 02:31:17,374] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:31:17,374] INFO [__main__:34] Configuration loaded
[2025-08-02 02:31:17,496] INFO [crawler9000.gui.main_window:49] MainWindow initialized
[2025-08-02 02:31:17,500] INFO [__main__:49] Crawler9000 GUI started
[2025-08-02 02:32:16,141] INFO [root:47] Logging setup complete.
[2025-08-02 02:32:16,141] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 02:32:16,141] INFO [__main__:34] Configuration loaded
[2025-08-02 02:32:16,256] INFO [crawler9000.gui.main_window:49] MainWindow initialized
[2025-08-02 02:32:16,260] INFO [__main__:49] Crawler9000 GUI started
[2025-08-02 03:18:26,263] INFO [root:47] Logging setup complete.
[2025-08-02 03:18:26,263] INFO [__main__:30] Starting Crawler9000...
[2025-08-02 03:18:26,264] INFO [__main__:34] Configuration loaded
[2025-08-02 19:06:57,308] INFO [root:47] Logging setup complete.
[2025-08-02 19:06:57,309] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:06:57,309] INFO [__main__:86] Configuration loaded
[2025-08-02 19:06:57,388] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:06:57,446] ERROR [crawler9000.gui.main_window:622] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:06:57,454] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:07:24,570] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:07:24,571] ERROR [__main__:24] Failed to emit error signal: QMutex(): too many arguments
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    with QMutex(self.mutex):
         ^^^^^^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:07:32,576] INFO [__main__:40] User chose to retry after unhandled exception
[2025-08-02 19:08:09,871] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    self.mutex.lock()
         ^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:08:09,871] ERROR [__main__:24] Failed to emit error signal: QMutex(): too many arguments
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    self.mutex.lock()
         ^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:08:11,993] INFO [__main__:40] User chose to retry after unhandled exception
[2025-08-02 19:08:13,336] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    self.mutex.lock()
         ^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:08:13,336] ERROR [__main__:24] Failed to emit error signal: QMutex(): too many arguments
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 554, in log_error
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 506, in append_log
    self.mutex.lock()
         ^^^^^^^^^^^^^
TypeError: QMutex(): too many arguments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:08:17,630] INFO [__main__:43] User chose to exit after unhandled exception
[2025-08-02 19:08:29,365] INFO [root:47] Logging setup complete.
[2025-08-02 19:08:29,365] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:08:29,365] INFO [__main__:86] Configuration loaded
[2025-08-02 19:08:29,451] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:08:29,515] ERROR [crawler9000.gui.main_window:622] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:08:29,523] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:08:41,921] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 557, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 525, in append_log
    widget.append(text)
    ^^^^^^^^^^^^^
AttributeError: 'QPlainTextEdit' object has no attribute 'append'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:08:41,923] ERROR [__main__:24] Failed to emit error signal: 'MainWindow' object has no attribute 'status_label'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 557, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 525, in append_log
    widget.append(text)
    ^^^^^^^^^^^^^
AttributeError: 'QPlainTextEdit' object has no attribute 'append'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:09:58,086] INFO [root:47] Logging setup complete.
[2025-08-02 19:09:58,086] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:09:58,086] INFO [__main__:86] Configuration loaded
[2025-08-02 19:09:58,177] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:09:58,246] ERROR [crawler9000.gui.main_window:622] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:09:58,254] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:10:02,132] ERROR [crawler9000.shared.signal_bus:384] Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 557, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 525, in append_log
    widget.append(text)
    ^^^^^^^^^^^^^
AttributeError: 'QPlainTextEdit' object has no attribute 'append'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:10:02,134] ERROR [__main__:24] Failed to emit error signal: 'MainWindow' object has no attribute 'status_label'
Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 483, in handle_error
    self.log_error(error_msg)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 557, in log_error
    self.append_log(3, error_text, logging.ERROR)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/gui/activity_monitor.py", line 525, in append_log
    widget.append(text)
    ^^^^^^^^^^^^^
AttributeError: 'QPlainTextEdit' object has no attribute 'append'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 362, in emit
    self._emitter.emit_signal(signal_type, payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 153, in emit_signal
    qt_signal.emit(payload)
SystemError: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/main.py", line 21, in handle_unhandled_exception
    emit_signal(SignalType.ERROR_OCCURRED, error_payload)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 561, in emit_signal
    get_signal_bus().emit(signal_type, payload, sender_id)
  File "/home/<USER>/Documents/Crawler9000/src/crawler9000/shared/signal_bus.py", line 386, in emit
    raise SignalBusError(error_msg) from e
crawler9000.shared.signal_bus.SignalBusError: Error emitting signal SignalType.ERROR_OCCURRED: <method 'emit' of 'PyQt6.QtCore.pyqtBoundSignal' objects> returned a result with an exception set
[2025-08-02 19:10:06,812] INFO [__main__:43] User chose to exit after unhandled exception
[2025-08-02 19:11:43,222] INFO [root:47] Logging setup complete.
[2025-08-02 19:11:43,222] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:11:43,222] INFO [__main__:86] Configuration loaded
[2025-08-02 19:11:43,315] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:11:43,376] ERROR [crawler9000.gui.main_window:622] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:11:43,384] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:12:13,611] INFO [crawler9000.gui.main_window:1163] Session auto-saved to /tmp/crawler9000_autosave/autosave_20250802_191213.json
[2025-08-02 19:12:13,612] INFO [crawler9000.utils.config:223] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 19:12:13,612] INFO [crawler9000.gui.main_window:1212] MainWindow closed gracefully with auto-save
[2025-08-02 19:12:13,621] INFO [__main__:108] Application exited with code: 0
[2025-08-02 19:12:50,599] INFO [root:47] Logging setup complete.
[2025-08-02 19:12:50,599] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:12:50,599] INFO [__main__:86] Configuration loaded
[2025-08-02 19:12:50,681] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:12:50,736] ERROR [crawler9000.gui.main_window:622] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:12:50,745] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:14:44,513] INFO [root:47] Logging setup complete.
[2025-08-02 19:14:44,514] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:14:44,514] INFO [__main__:86] Configuration loaded
[2025-08-02 19:14:44,597] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:14:44,658] ERROR [crawler9000.gui.main_window:622] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:14:44,666] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:15:24,905] INFO [crawler9000.gui.main_window:1163] Session auto-saved to /tmp/crawler9000_autosave/autosave_20250802_191524.json
[2025-08-02 19:15:24,906] INFO [crawler9000.utils.config:223] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 19:15:24,906] INFO [crawler9000.gui.main_window:1212] MainWindow closed gracefully with auto-save
[2025-08-02 19:15:24,907] INFO [__main__:108] Application exited with code: 0
[2025-08-02 19:21:32,879] INFO [root:47] Logging setup complete.
[2025-08-02 19:21:32,880] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:21:32,880] INFO [__main__:86] Configuration loaded
[2025-08-02 19:21:32,979] INFO [crawler9000.core.url_manager:57] URLManager initialized with thread-safe PriorityQueue
[2025-08-02 19:21:32,979] INFO [crawler9000.core.content_parser:24] ContentParser initialized
[2025-08-02 19:22:15,652] INFO [root:47] Logging setup complete.
[2025-08-02 19:22:15,653] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 19:22:15,653] INFO [__main__:86] Configuration loaded
[2025-08-02 19:22:15,748] INFO [crawler9000.core.url_manager:57] URLManager initialized with thread-safe PriorityQueue
[2025-08-02 19:22:15,748] INFO [crawler9000.core.content_parser:24] ContentParser initialized
[2025-08-02 19:22:15,748] WARNING [crawler9000.core.ai_integration:124] No AI API key provided. AI features will be disabled.
[2025-08-02 19:22:15,748] INFO [crawler9000.core.ai_integration:126] AIAnalyzer initialized with provider: deepseek
[2025-08-02 19:22:15,748] INFO [crawler9000.core.crawler_engine:52] CrawlerEngine initialized with all components
[2025-08-02 19:22:15,749] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 19:22:15,844] ERROR [crawler9000.gui.main_window:640] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 19:22:15,857] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 19:23:36,898] INFO [crawler9000.gui.main_window:1274] Session auto-saved to /tmp/crawler9000_autosave/autosave_20250802_192336.json
[2025-08-02 19:23:36,900] INFO [crawler9000.utils.config:223] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 19:23:36,900] INFO [crawler9000.gui.main_window:1323] MainWindow closed gracefully with auto-save
[2025-08-02 19:23:36,900] INFO [__main__:108] Application exited with code: 0
[2025-08-02 20:38:51,599] INFO [root:47] Logging setup complete.
[2025-08-02 20:38:51,599] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 20:38:51,599] INFO [__main__:86] Configuration loaded
[2025-08-02 20:38:51,685] INFO [crawler9000.core.url_manager:57] URLManager initialized with thread-safe PriorityQueue
[2025-08-02 20:38:51,686] INFO [crawler9000.core.content_parser:24] ContentParser initialized
[2025-08-02 20:38:51,686] WARNING [crawler9000.core.ai_integration:124] No AI API key provided. AI features will be disabled.
[2025-08-02 20:38:51,686] INFO [crawler9000.core.ai_integration:126] AIAnalyzer initialized with provider: deepseek
[2025-08-02 20:38:51,686] INFO [crawler9000.core.crawler_engine:53] CrawlerEngine initialized with all components
[2025-08-02 20:38:51,687] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 20:38:51,749] ERROR [crawler9000.gui.main_window:643] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 20:38:51,757] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 20:39:39,388] INFO [crawler9000.gui.main_window:1314] Session auto-saved to /tmp/crawler9000_autosave/autosave_20250802_203939.json
[2025-08-02 20:39:39,389] INFO [crawler9000.utils.config:223] Saved config to /home/<USER>/.config/crawler9000/user_config.toml
[2025-08-02 20:39:39,389] INFO [crawler9000.gui.main_window:1363] MainWindow closed gracefully with auto-save
[2025-08-02 20:39:39,396] INFO [__main__:108] Application exited with code: 0
[2025-08-02 20:41:07,630] INFO [root:47] Logging setup complete.
[2025-08-02 20:41:07,630] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 20:41:07,630] INFO [__main__:86] Configuration loaded
[2025-08-02 20:41:07,705] INFO [crawler9000.core.url_manager:57] URLManager initialized with thread-safe PriorityQueue
[2025-08-02 20:41:07,706] INFO [crawler9000.core.content_parser:24] ContentParser initialized
[2025-08-02 20:41:07,706] WARNING [crawler9000.core.ai_integration:124] No AI API key provided. AI features will be disabled.
[2025-08-02 20:41:07,706] INFO [crawler9000.core.ai_integration:126] AIAnalyzer initialized with provider: deepseek
[2025-08-02 20:41:07,706] INFO [crawler9000.core.crawler_engine:53] CrawlerEngine initialized with all components
[2025-08-02 20:41:07,706] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 20:41:07,764] ERROR [crawler9000.gui.main_window:643] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 20:41:07,773] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 20:44:48,004] INFO [crawler9000.core.crawler_engine:63] Starting crawl with 1 URLs: ['https://api-docs.deepseek.com/']
[2025-08-02 20:44:48,005] ERROR [crawler9000.core.crawler_service:56] Error running crawler: 'CrawlerConfig' object has no attribute 'crawler'
[2025-08-02 20:46:19,004] INFO [__main__:43] User chose to exit after unhandled exception
[2025-08-02 20:49:16,946] INFO [root:47] Logging setup complete.
[2025-08-02 20:49:16,946] INFO [__main__:82] Starting Crawler9000...
[2025-08-02 20:49:16,946] INFO [__main__:86] Configuration loaded
[2025-08-02 20:49:17,030] INFO [crawler9000.core.url_manager:57] URLManager initialized with thread-safe PriorityQueue
[2025-08-02 20:49:17,031] INFO [crawler9000.core.content_parser:24] ContentParser initialized
[2025-08-02 20:49:17,031] WARNING [crawler9000.core.ai_integration:124] No AI API key provided. AI features will be disabled.
[2025-08-02 20:49:17,031] INFO [crawler9000.core.ai_integration:126] AIAnalyzer initialized with provider: deepseek
[2025-08-02 20:49:17,031] INFO [crawler9000.core.crawler_engine:53] CrawlerEngine initialized with all components
[2025-08-02 20:49:17,032] INFO [crawler9000.shared.signal_bus:196] SignalBus initialized as singleton
[2025-08-02 20:49:17,086] ERROR [crawler9000.gui.main_window:643] Error loading layout geometry: restoreState(self, state: Union[QByteArray, bytes, bytearray, memoryview], version: int = 0): argument 1 has unexpected type 'list'
[2025-08-02 20:49:17,094] INFO [__main__:101] Crawler9000 GUI started
[2025-08-02 20:51:26,754] INFO [crawler9000.core.crawler_engine:63] Starting crawl with 1 URLs: ['https://api-docs.deepseek.com/']
[2025-08-02 20:51:26,755] INFO [crawler9000.core.async_url_fetcher:76] AsyncURLFetcher initialized
[2025-08-02 20:51:26,755] INFO [crawler9000.core.async_url_fetcher:115] HTTP session initialized with connection pooling
[2025-08-02 20:51:26,755] INFO [crawler9000.core.crawler_engine:161] Processing URL: https://api-docs.deepseek.com/ with metadata: {'priority': 1.0, 'depth': 0, 'parent_url': None, 'added_at': 1754178686.7549067, 'actual_priority': 1.0, 'queued_at': 1754178686.7549067}
[2025-08-02 20:51:27,232] INFO [crawler9000.core.async_url_fetcher:158] Successfully fetched https://api-docs.deepseek.com/ (42410 bytes)
[2025-08-02 20:51:27,260] INFO [crawler9000.core.content_parser:63] Parsed page: https://api-docs.deepseek.com/ (266 words, 4 links)
[2025-08-02 20:51:27,262] INFO [crawler9000.core.crawler_engine:250] Processed https://api-docs.deepseek.com/: 266 words, 3 new links
[2025-08-02 20:51:28,264] INFO [crawler9000.core.async_url_fetcher:122] HTTP session closed
[2025-08-02 20:51:28,300] INFO [crawler9000.core.crawler_engine:141] Crawl completed. Stats: {'visited_urls': 1, 'queued_urls': 0, 'total_discovered': 1, 'pages_crawled': 1, 'is_running': True, 'start_domain': 'api-docs.deepseek.com'}
