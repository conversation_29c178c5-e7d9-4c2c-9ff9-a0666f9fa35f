/* Custom CSS for Crawler9000 Documentation */

/* Code blocks styling */
.highlight {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin: 1em 0;
}

/* API documentation styling */
.class > dt {
    background-color: #f1f3f4;
    border-left: 4px solid #4285f4;
    padding: 8px 12px;
}

.method > dt {
    background-color: #f8f9fa;
    border-left: 4px solid #34a853;
    padding: 6px 10px;
}

.function > dt {
    background-color: #fef7e0;
    border-left: 4px solid #fbbc04;
    padding: 6px 10px;
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    margin: 1em 0;
}

table.docutils th,
table.docutils td {
    border: 1px solid #ddd;
    padding: 8px 12px;
}

table.docutils th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Note and warning boxes */
.admonition {
    margin: 1em 0;
    padding: 1em;
    border-radius: 4px;
}

.admonition.note {
    background-color: #e8f4fd;
    border-left: 4px solid #0969da;
}

.admonition.warning {
    background-color: #fff8dc;
    border-left: 4px solid #d1242f;
}

.admonition.tip {
    background-color: #f6ffed;
    border-left: 4px solid #52c41a;
}

/* Improve readability of parameter lists */
.field-list {
    margin-bottom: 1em;
}

.field-name {
    font-weight: bold;
    color: #333;
}

.field-body {
    margin-left: 1em;
}

/* Version badges */
.versionadded,
.versionchanged,
.deprecated {
    font-size: 0.9em;
    padding: 2px 6px;
    border-radius: 3px;
    margin: 0 4px;
}

.versionadded {
    background-color: #d4edda;
    color: #155724;
}

.versionchanged {
    background-color: #fff3cd;
    color: #856404;
}

.deprecated {
    background-color: #f8d7da;
    color: #721c24;
}
